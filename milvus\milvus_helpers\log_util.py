#!/usr/bin/python
#coding:utf-8

import logging,os
import logging.handlers
import sys

#日志内容格式
message_format  = '%(asctime)s %(levelname)s %(pathname)s(line:%(lineno)d) %(message)s'

file_dir = os.path.abspath(os.path.dirname(__file__))
root_path=file_dir.split("/")[:-1]
root_dir="/".join(root_path)
print(f"日志的根目录文件路径：{root_dir}")
sys.path.append(root_dir)  # 临时修改环境变量

def getDayLog(filename="my.log",set_log_path=root_dir,interval=1,when="MIDNIGHT",backupCount=3):
    # 创建日志目录
    log_path = os.path.join(set_log_path, 'logs')
    if not os.path.exists(log_path):
        os.makedirs(log_path)
    '''
    获取日志对象，24小时拆分一次

    filename:日志文件名或绝对路基加文件名，默认目录logs,文件名my-day.log，按天自动拆分
    when：间隔多久拆分一次，默认MIDNIGHT，每天过0点执行，可填写：S、M、H、D（MIDNIGHT）、W
    backupCount:存储拆分文件个数
    '''
    log_file = os.path.join(set_log_path,'logs',filename)

    # logging初始化工作
    '''
    level:  控制台打印的日志级别
    format:日志格式
    '''
    logging.basicConfig(level=logging.INFO,format=message_format)
    #初始化
    log = logging.getLogger(filename) #定义对应程序模块名，默认root
    log.setLevel(logging.INFO)

    if not log.handlers:
        file =logging.handlers.TimedRotatingFileHandler(log_file,when=when,interval=interval,backupCount=backupCount,encoding='utf-8')

        # file = logging.handlers.TimedRotatingFileHandler(log_file, when='S', interval=2, backupCount=3,
        #                                                   encoding='utf-8')

        # 设置后缀名称，跟strftime的格式一样
        file.suffix = "%Y-%m-%d.log"
        #file.suffix = "%Y-%m-%d_%H-%M-%S.log"
        file.setFormatter(logging.Formatter(message_format))
        log.addHandler(file)

    return log


if __name__ == '__main__':
    root_dir = os.path.abspath(os.path.dirname(__file__))
    #'/media/vdb/project/hwzx/tempt_hscode_code/zte-dmt-dmp-zxnlp/hscode-classification/server/util'
    sys.path.append(root_dir)  # 临时修改环境变量
    print(root_dir)