import sys
import requests
sys.path.append('milvus_helpers')
from milvus_helpers import MilvusHelper

def embedding_doc(query):
    """生成查询文本的向量嵌入"""
    try:
        receive = requests.post( 
            json={"text": query},
            url="https://llm.dev.zte.com.cn/zte-igpt-ellm-vector/vector",
            verify=False,
            timeout=30
        )
        result = receive.json()['bo']['sentence_embeddings']
        return result
    except Exception as e:
        print(f"向量生成失败: {e}")
        return None

def test_search():
    """测试向量搜索功能"""
    # 初始化Milvus连接
    milvushelper = MilvusHelper()
    collection_name = "parsed_pdf_chunks"
    
    # 检查集合是否存在
    if not milvushelper.has_collection(collection_name):
        print(f"集合 '{collection_name}' 不存在，请先运行导入脚本")
        return
    
    # 获取数据总数
    count = milvushelper.count(collection_name)
    print(f"向量库中共有 {count} 条记录")
    
    if count == 0:
        print("向量库为空，请先导入数据")
        return
    
    # 测试搜索
    while True:
        query = input("\n请输入搜索关键词 (输入 'quit' 退出): ")
        if query.lower() == 'quit':
            break
            
        if not query.strip():
            continue
            
        # 生成查询向量
        print("正在生成查询向量...")
        query_vector = embedding_doc(query)
        if query_vector is None:
            print("查询向量生成失败")
            continue
            
        # 执行搜索
        print("正在搜索...")
        try:
            results = milvushelper.search_vectors(collection_name, [query_vector], top_k=5)
            
            if not results or not results[0]:
                print("未找到相关结果")
                continue
                
            print(f"\n找到 {len(results[0])} 个相关结果:")
            print("-" * 80)
            
            for i, hit in enumerate(results[0]):
                print(f"结果 {i+1}:")
                print(f"  相似度分数: {hit.score:.4f}")
                print(f"  文档ID: {hit.id}")
                # 注意：这里需要根据实际的字段名调整
                if hasattr(hit, 'entity') and hasattr(hit.entity, 'document'):
                    content = hit.entity.document[:200] + "..." if len(hit.entity.document) > 200 else hit.entity.document
                    print(f"  内容预览: {content}")
                print("-" * 40)
                
        except Exception as e:
            print(f"搜索失败: {e}")

if __name__ == "__main__":
    test_search()
