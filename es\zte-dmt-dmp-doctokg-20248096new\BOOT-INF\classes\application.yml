spring:
  application:
    name: zte-dmt-dmp-datadeal
  profiles:
    active: loc
  http:
    encoding:
      force: true
      forceRequest: true
      forceResponse: true
    multipart:
      max-file-size: 2048MB
      max-request-size: 2048MB

server:
  port: 8096
  tomcat:
    uri-encoding: UTF-8

  servlet:
    context-path: /${spring.application.name}/

logging:
  config: classpath:logback-spring.xml

#指定swagger api访问方式
springfox:
  documentation:
    swagger:
      v2:
        path: /swagger.json
info:
  status: ok

management:
  endpoints:
    web:
      base-path: /
      exposure:
        include: [ "info" ]

auth:
  server:
    enabled: true
    encryptionKey: yxjPswPqhAKFkBAoa5ht9V0NeP+i7Cp3eZ
    interval: 60