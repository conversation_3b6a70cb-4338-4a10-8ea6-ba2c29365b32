from pymilvus import connections, FieldSchema, CollectionSchema, DataType, Collection, utility
import sys
sys.path.append('..')
from config import MILVUS_HOST, MILVUS_PORT, VECTOR_DIMENSION, METRIC_TYPE
from logs import LOGGER


class MilvusHelper:

    def __init__(self):
        try:
            self.collection = None
            connections.connect(host=MILVUS_HOST, port=MILVUS_PORT)
            LOGGER.info(f"Successfully connect to Milvus with IP:{MILVUS_HOST} and PORT:{MILVUS_PORT}")
        except Exception as e:
            LOGGER.error(f"Failed to connect Milvus: {e}")
            raise Exception(f"Failed to connect Milvus: {e}")

    # Return if <PERSON>lvus has the collection
    def has_collection(self, collection_name):
        try:
            return utility.has_collection(collection_name)
        except Exception as e:
            LOGGER.error(f"Failed to return if <PERSON><PERSON><PERSON><PERSON> has the collection: {e}")
            raise Exception(f"Failed to return if <PERSON><PERSON><PERSON><PERSON> has the collection: {e}")

    def set_collection(self, collection_name):
        try:
            if self.has_collection(collection_name):
                self.collection = Collection(name=collection_name)
            else:
                raise Exception(f"There is no collection named:{collection_name}")
        except Exception as e:
            LOGGER.error(f"Failed to connect collection: {e}")
            raise Exception(f"Failed to connect collection: {e}")

    # Create milvus collection if not exists
    def create_collection(self, collection_name):
        try:
            if not self.has_collection(collection_name):
                
                field1 = FieldSchema(name="id", dtype=DataType.VARCHAR, descrition="varchar",
                                     is_primary=True, auto_id=False, max_length=65535)
                field2 = FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, descrition="float vector",
                                     dim=VECTOR_DIMENSION, is_primary=False)
                field3 = FieldSchema(name="document",dtype=DataType.VARCHAR,description="varchar",is_primary=False,
                                     auto_id=False,max_length=65535)
                field4 = FieldSchema(name="doc_name",dtype=DataType.VARCHAR,description="varchar",is_primary=False,
                                     auto_id=False,max_length=65535)
                schema = CollectionSchema(fields=[field1,field2,field3,field4], description="collection description")
                self.collection = Collection(name=collection_name, schema=schema)
                LOGGER.info(f"Create Milvus collection: {collection_name}")
                self.create_index(collection_name)
                self.collection.load()
            else:
                self.set_collection(collection_name)
            return "OK"
        except Exception as e:
            LOGGER.error(f"Failed to create milvus collection if not exists: {e}")
            raise Exception(f"Failed to create milvus collection if not exists: {e}")

    # Batch insert vectors to milvus collection
    def insert(self, collection_name, vectors, names,doc_name):
        try:
            self.create_collection(collection_name)
            data = [names, vectors,doc_name]
            self.set_collection(collection_name)
            mr = self.collection.insert(data)
            ids = mr.primary_keys
            self.collection.load()
            LOGGER.info(f"Insert vectors to Milvus in collection: {collection_name} with {len(vectors)} rows")
            return ids
        except Exception as e:
            LOGGER.error(f"Failed to insert data to Milvus: {e}")
            raise Exception(f"Failed to insert data to Milvus: {e}")

    def insert_doc(self, collection_name, id,vectors,doc,doc_name):
        try:
            self.create_collection(collection_name)
            data = [id,vectors,doc,doc_name]
            self.set_collection(collection_name)
            mr = self.collection.insert(data)
            ids = mr.primary_keys
            self.collection.load()
            LOGGER.info(f"Insert vectors to Milvus in collection: {collection_name} with {len(vectors)} rows")
            return ids
        except Exception as e:
            LOGGER.error(f"Failed to insert data to Milvus: {e}")
            raise Exception(f"Failed to insert data to Milvus: {e}")
    
    def load(self, collection_name):
        try:
            self.set_collection(collection_name)
            self.collection.load()
        except Exception as e:
            LOGGER.error(f"Failed to load data: {e}")
            raise Exception(f"Failed to load data: {e}")

    def create_index(self, collection_name):
        # Create IVF_FLAT index on milvus collection
        try:
            self.create_collection(collection_name)
            if not self.collection.has_index():
                default_index = {"index_type": "IVF_FLAT", "metric_type": METRIC_TYPE, "params": {"nlist": 16384}}
                status = self.collection.create_index(field_name="embedding", index_params=default_index)
                if not status.code:
                    LOGGER.info(f"Successfully create index in collection:{collection_name} with param:{default_index}")
                    return status
                else:
                    raise Exception(status.message)
            else:
                LOGGER.info(f"collection:{collection_name} have index")
        except Exception as e:
            LOGGER.error(f"Failed to create index: {e}")
            raise Exception(f"Failed to create index: {e}")

    def drop_index(self, collection_name):
        try:
            self.set_collection(collection_name)
            if self.collection.has_index():
                status = self.collection.drop_index()
        except Exception as e:
            LOGGER.error(f"Failed to drop index: {e}")
            raise Exception(f"Failed to drop index: {e}")

    def has_index(self, collection_name):
        try:
            self.set_collection(collection_name)
            status = self.collection.has_index()
            if not status:
                LOGGER.debug(f"collection:{collection_name} have index")
                return status
            else:
                LOGGER.debug(f"collection:{collection_name} don't have index")
                return status
        except Exception as e:
            LOGGER.error(f"Failed to return if collection has the index: {e}")
            raise Exception(f"Failed to return if collection has the index: {e}")

    def delete_collection(self, collection_name):
        # Delete Milvus collection
        try:
            self.set_collection(collection_name)
            self.collection.drop()
            LOGGER.debug("Successfully drop collection!")
            return "ok"
        except Exception as e:
            LOGGER.error(f"Failed to drop collection: {e}")
            raise Exception(f"Failed to drop collection: {e}")

    def delete_by_ids(self, ids, collection_name):
        # Delete Milvus collection
        try:
            self.set_collection(collection_name)
            ids = str(ids).replace('\'', '\"')
            self.collection.delete(expr=f"id in {ids}")
            LOGGER.debug("Successfully delete vectors!")
            return "ok"
        except Exception as e:
            LOGGER.error(f"Failed to delete vectors: {e}")
            raise Exception(f"Failed to delete vectors: {e}")

    def search_vectors(self, collection_name, vectors, top_k):
        # Search vector in milvus collection
        try:
            if not self.has_collection(collection_name):
                return []
            self.set_collection(collection_name)
            # search_params = {"metric_type": METRIC_TYPE, "params": {"ef": 256}}
            search_params = {"metric_type": METRIC_TYPE, "params": {"nprobe": 16}}
            res = self.collection.search(vectors, anns_field="embedding", param=search_params, limit=top_k)
            LOGGER.debug(f"Successfully search in collection: {res}")
            return res
        except Exception as e:
            LOGGER.error(f"Failed to search vectors in Milvus: {e}")
            raise Exception(f"Failed to search vectors in Milvus: {e}")

    def count(self, collection_name):
        # Get the number of milvus collection
        try:
            self.set_collection(collection_name)
            num = self.collection.num_entities
            LOGGER.debug(f"milvus count,collection:{collection_name},num:{num}")
            return num
        except Exception as e:
            LOGGER.error(f"Failed to count vectors in Milvus: {e}")
            raise Exception(f"Failed to count vectors in Milvus: {e}")

    def query_by_ids(self, ids, collection_name):
        # Delete Milvus collection
        try:
            self.set_collection(collection_name)
            ids = str(ids).replace('\'', '\"')
            print(ids)
            res = self.collection.query(expr=f"id in {ids}", output_fields=['id', 'embedding','doc_id'])
            return res
        except Exception as e:
            LOGGER.error(f"Failed to query vectors: {e}")
            raise Exception(f"Failed to query vectors: {e}")

    def compact(self, collection_name):
        try:
            self.set_collection(collection_name)
            self.collection.compact()
            print(self.collection.get_compaction_state())
            self.collection.wait_for_compaction_completed()
            return self.collection.get_compaction_state()
        except Exception as e:
            LOGGER.error(f"Failed to compact collection: {e}")
            raise Exception(f"Failed to compact collection: {e}")
