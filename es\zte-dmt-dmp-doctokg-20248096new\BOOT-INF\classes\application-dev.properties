servicecenter = msb
servicecenter = msb
register.address = msruat.zte.com.cn:10081
register.node.ip = kgertest.zte.com.cn
register.node.port = 8888

## ???????????8192
server.tomcat.max-connections = 10000
## ?????????100?
server.tomcat.accept-count = 1000
## ??????????200?
server.tomcat.threads.max = 1000
## ????????????10??????????????????????
server.tomcat.threads.min-spare = 100


dmp.dmt.baseUrl = http://kgertest.zte.com.cn/

common.otherMap[business.targets] = ??,??????,????,????,??PO,??????,GTS????,??,???,???,????,??????,???,????,?????,???,??,?????,??,??????,??????,ERP????,????,DO?,DN?,????,????,??????,?????,?????,???????,????????,??????,????,DO_DN,??ERP??,ERP????,??????,??????
common.otherMap[search.office] = representative_office
common.otherMap[upp.supperGroupId] = 10570791
common.otherMap[fileCloudUrl] = https://idrive.zte.com.cn/zte-km-cloududm-properties/tag-job/add

cloud.X-Origin-ServiceName = DMP_KGCOMPLIANCE
cloud.X-Org-Id = 369e705d2f6667f59e1b41e4852178d3c9f4125c
cloud.secretKey = 45a5025b45a04efe87bd66f1a8ee2c6b
cloud.accessKey = 369e705d2f6667f59e1b41e4852178d3c9f4125c

common.otherMap[url.searchEntityList] = /nebula/searchEntityList
common.otherMap[url.searchEntityMap] = /nebula/searchEntityMap
common.otherMap[url.queryTableData] = /nebula/queryTableData
common.otherMap[url.searchES] = /es/suggest
common.otherMap[url.link] = /es/termQueryNoRepeat
common.otherMap[url.queryHbase] = /hbase/fetchHbaseRecordWithRowKeys
common.otherMap[url.queryPath] = /nebula/searchEntityPathList

common.otherMap[url.getEnglishMapping] = http://kgertest.zte.com.cn/zte-dmt-dmp-kgnlp/knowledgeGraph/getEnglishMapping

#redis??
#?????????
msa.ccs.enable = true
#??????????????????
msa.ccs.resourceCode = dmt-dmp-kgcompliance
cacheCloud.server = http://uat.sz-cache.zcloud.zte.com.cn:8888/zte-itp-msp-cachecloud/
msa.redis.driveType = jedis
redis.mode = 4

#hbase.status.table.name=kgc_status_tables_hbase
#hbase.fileTag.table.name=file_tag_table
hbase.status.table.name = kgc_status_tables_hbase0317
hbase.fileTag.table.name = file_tag_table0317
nebula.space.name = scc001
es.index.name = scc001entity_linking
gray.scale.empNo = 10261251
export.amount.limit = 5000

spring.datasource.driver-class-name = com.mysql.jdbc.Driver
spring.datasource.url = **************************************************************************************************************************************************************************************************************************************************************************************************
spring.datasource.username = dmp
spring.datasource.password = 68?1F?72!14?AA?1D?2D$63?8B!0C$E8$2B!0F$1F!2D#DF#
mybatis.type-aliases-package = com.zte.dmt.dmp.entity
mybatis.configuration.mapUnderscoreToCamelCase = true

mails.server = http://itpmail.zte.com.cn/mailService/services/MailTemplateService?wsdl
; mails.server = http://10.54.141.169//mailService/services/MailTemplateService?wsdl
mails.mailTo = 10261251,10257736

#UAC????
uac.systemCode = 100000455594
#UAC?????
uac.token.baseUrl = https://uactest.zte.com.cn:8080/uactoken
#??????
uac.token.enabled = true
#???uac?????????
uac.token.failedCode = 401
#web??????
#????web????
uac.web.enabled = false
#UAC????
#UAC ???key???
uac.auth.accessKey = dmpc7fxmyspvl992kgri8pky6avbbwn7
uac.auth.accessSecret = 030cab535836f348d4867a7bd5e3c29f77978232ea0a92a6b640c1b32af87f5b
#????????
uac.cache.initialCapacity = 500
#????????
uac.cache.maximumSize = 500
#?????????
uac.cache.cacheTime = 300

#????
upp.auth.productId = 1711001
upp.auth.moduleId = 2010008
upp.auth.tenantId = 4304
upp.auth.productSecretKey = PgyBrVNvzDZ39vPZqlLoZSHhcW4it0ZJ
upp.auth.authUrl = https://uactest.zte.com.cn/zte-sec-upp-authbff
upp.auth.manageUrl = https://uactest.zte.com.cn/zte-sec-upp-bff
upp.auth.cacheMaxSize = 10000
upp.auth.cacheInitSize = 1000
upp.control.enable = true

taskApplicationName = mail_warning_task
getTaskConfigUrl = http://kgertest.zte.com.cn/zte-dmt-dmp-kgnlp/taskConfig/getTaskConfigByName?applicationName=
updateTaskConfigUrl = http://kgertest.zte.com.cn/zte-dmt-dmp-kgnlp/taskConfig/updateConfig
algorithm = HmacSHA256
devAccessSecret = q2hrt6klo67oxswz5munsmoub7uyqkt5
prodAccessSecret = 100000451408gkh5njs83opf

nebula.hosts = **************:9669
nebula.username = root
nebula.password = CE?76#1F!3C!72?C7?5A?77?E9!B4?5B!11#B9!5B!4D!1A#
userprofile.nebula.url = /nebula/executeNql

userprofile.url = http://kgertest.zte.com.cn/zte-dmt-dmp-kgbff/zte-dmt-dmp-userprofile-bff-new
#userprofile.url=http://kgertest.zte.com.cn/zte-dmt-dmp-kgbff/zte-dmt-dmp-userprofile-bff-new
#userprofile.url=http://*************:8036/zte-dmt-dmp-userprofile
#userprofile.url=http://localhost:8036/zte-dmt-dmp-userprofile/

userprofile.es.suggest.url = /es/suggestLink
userprofile.es.link.url = /es/commonSearch

system.accesskey = qb1xtquktej4hk53qm7wwroyizmkb2fy
system.accessSecret = aebcc6690fbc0b11ef0ac2bcd008ed4d8b8e8f141e5fd446ab396082ae926916
system.env = dev
systemName = kgcompliance

mybatis.configuration.default-statement-timeout = 500

kg.minio.url = http://kgertest.zte.com.cn/zte-dmt-dmp-kgbff/kg-minio-new
contractFileDownload.amount.limit = 5
kgcompliance.bucketName = kgcompliance
userprofile.es.termquery.url = 111

es.documentIndex = contract_document_index_new
before.2018.index = contract_color_data_index_before_2018_new
color.index.prefix = contract_color_data_index_new_

es.representIndexName = representative_index

nebula.subSpace.name = kangxun
sub.es.index.name = kangxunentity_linking

kg_cloud_url = http://kgertest.zte.com.cn/zte-dmt-dmp-kgbff/kg-clouddisk-center
es.contractFieldIndexName = contract_field_index
aes.algorithm = Aes
aes.encryptKey = 098f6bcd4621d373
aes.viKey = cade4e832627b4f6
