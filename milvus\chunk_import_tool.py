import time
import os
import sys
import json
from milvus_helpers import MilvusHelper
from pymilvus import utility, Collection
import requests

# 添加milvus_helpers路径
sys.path.append('milvus_helpers')

# 初始化Milvus连接
milvushelper = MilvusHelper()
collection_name = "parsed_pdf_chunks"
milvushelper.create_collection(collection_name)
collection = Collection(collection_name)
print(f"连接到集合: {collection}")

# 异常文件记录路径
except_file_path = "chunk_import_exceptions.txt"

def truncate_string(s, max_bytes):
    """截断字符串到指定字节长度"""
    encoded_string = s.encode('utf-8')
    if len(encoded_string) <= max_bytes:
        return s
    truncated_bytes = encoded_string[:max_bytes]
    truncated_string = truncated_bytes.decode('utf-8', 'ignore')
    return truncated_string

def embedding_doc(query):
    """调用ZTE内部API生成文本向量嵌入"""
    try:
        if isinstance(query, list):
            # 如果是列表，只取第一个元素
            text = query[0] if query else ""
        else:
            text = query
            
        receive = requests.post( 
            json={"text": text},
            url="https://llm.dev.zte.com.cn/zte-igpt-ellm-vector/vector",
            verify=False,
            timeout=30  # 添加超时设置
        )
        result = receive.json()['bo']['sentence_embeddings']
        return result
    except Exception as e:
        print(f"向量生成失败: {e}")
        return None

def read_chunk_file(file_path):
    """读取chunk文件内容"""
    try:
        with open(file_path, "r", encoding='utf-8') as file:
            content = file.read().strip()
            return content
    except Exception as e:
        print(f"读取文件失败 {file_path}: {e}")
        return None

def load_metadata(metadata_path):
    """加载metadata.json文件"""
    try:
        with open(metadata_path, "r", encoding='utf-8') as file:
            metadata = json.load(file)
            return metadata
    except Exception as e:
        print(f"读取metadata失败: {e}")
        return None

def main():
    # 目标文件夹路径
    chunk_folder = r"D:\JAVA\workspace\AI\ZTE_AI\zte-ibo-acm-productretrieve_0804\text_chunks\parsed_example_pdf"
    
    print(f"开始导入文件夹: {chunk_folder}")
    
    # 加载metadata信息
    metadata_path = os.path.join(chunk_folder, "metadata.json")
    metadata = load_metadata(metadata_path)
    source_file = metadata.get('source_file', 'unknown') if metadata else 'unknown'
    
    # 获取所有chunk文件
    chunk_files = [f for f in os.listdir(chunk_folder) if f.startswith('chunk_') and f.endswith('.txt')]
    chunk_files.sort()  # 按文件名排序
    
    print(f"找到 {len(chunk_files)} 个chunk文件")
    
    # 批量处理数据
    content_list = []
    id_list = []
    vec_list = []
    doc_name_list = []
    
    failed_files = []
    
    for i, filename in enumerate(chunk_files):
        file_path = os.path.join(chunk_folder, filename)
        print(f"处理文件 {i+1}/{len(chunk_files)}: {filename}")
        
        # 读取chunk内容
        content = read_chunk_file(file_path)
        if content is None:
            failed_files.append(filename)
            continue
            
        # 截断内容到合适长度
        content = truncate_string(content, 65535)
        
        # 生成ID
        chunk_id = f"{source_file}_{filename}"
        
        # 添加到列表
        content_list.append(content)
        id_list.append(chunk_id)
        doc_name_list.append(source_file)
        
        # 生成向量嵌入
        try:
            print(f"  生成向量嵌入...")
            vec = embedding_doc(content)
            if vec is None:
                print(f"  向量生成失败，跳过文件: {filename}")
                failed_files.append(filename)
                # 移除已添加的数据
                content_list.pop()
                id_list.pop()
                doc_name_list.pop()
                continue
            vec_list.append(vec)
            print(f"  向量生成成功")
            
        except Exception as e:
            print(f"  向量生成异常: {e}")
            failed_files.append(filename)
            # 移除已添加的数据
            content_list.pop()
            id_list.pop()
            doc_name_list.pop()
            continue
        
        # 每处理10个文件就批量插入一次
        if len(content_list) >= 10:
            try:
                print(f"  批量插入 {len(content_list)} 条记录到向量库...")
                if len(content_list) == len(vec_list) == len(id_list) == len(doc_name_list):
                    milvushelper.insert_doc(collection_name, id_list, vec_list, content_list, doc_name_list)
                    print(f"  批量插入成功")
                    # 清空列表
                    content_list = []
                    id_list = []
                    vec_list = []
                    doc_name_list = []
                else:
                    print(f"  数据长度不匹配，跳过批量插入")
            except Exception as e:
                print(f"  批量插入失败: {e}")
                with open(except_file_path, "a+", encoding='utf-8') as f:
                    f.write(f"批量插入失败: {str(e)}\n")
    
    # 处理剩余的数据
    if content_list:
        try:
            print(f"插入剩余 {len(content_list)} 条记录...")
            if len(content_list) == len(vec_list) == len(id_list) == len(doc_name_list):
                milvushelper.insert_doc(collection_name, id_list, vec_list, content_list, doc_name_list)
                print(f"剩余数据插入成功")
            else:
                print(f"剩余数据长度不匹配")
        except Exception as e:
            print(f"剩余数据插入失败: {e}")
            with open(except_file_path, "a+", encoding='utf-8') as f:
                f.write(f"剩余数据插入失败: {str(e)}\n")
    
    # 输出统计信息
    total_files = len(chunk_files)
    success_files = total_files - len(failed_files)
    
    print(f"\n=== 导入完成 ===")
    print(f"总文件数: {total_files}")
    print(f"成功导入: {success_files}")
    print(f"失败文件数: {len(failed_files)}")
    
    if failed_files:
        print(f"失败文件列表:")
        for f in failed_files:
            print(f"  - {f}")
        
        # 记录失败文件
        with open(except_file_path, "a+", encoding='utf-8') as f:
            f.write(f"\n=== {time.strftime('%Y-%m-%d %H:%M:%S')} ===\n")
            for failed_file in failed_files:
                f.write(f"{failed_file}\n")
    
    # 检查集合中的数据量
    try:
        count = milvushelper.count(collection_name)
        print(f"向量库中总记录数: {count}")
    except Exception as e:
        print(f"获取记录数失败: {e}")

if __name__ == "__main__":
    main()
