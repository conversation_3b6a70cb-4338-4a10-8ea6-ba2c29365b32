2024-07-20 22:05:19,163 ｜ INFO ｜ milvus_helpers.py ｜ __init__ ｜ 14 ｜ Successfully connect to Milvus with IP:************* and PORT:19530
2024-07-20 22:05:19,292 ｜ INFO ｜ milvus_helpers.py ｜ create_collection ｜ 52 ｜ Create Milvus collection: productQA_qingxi_0720
2024-07-20 22:05:19,967 ｜ INFO ｜ milvus_helpers.py ｜ create_index ｜ 107 ｜ Successfully create index in collection:productQA_qingxi_0720 with param:{'index_type': 'IVF_FLAT', 'metric_type': 'COSINE', 'params': {'nlist': 16384}}
2024-07-20 22:05:24,142 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 19 rows
2024-07-20 22:05:27,671 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 46 rows
2024-07-20 22:05:29,188 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 21 rows
2024-07-20 22:05:36,590 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 71 rows
2024-07-20 22:05:41,284 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 29 rows
2024-07-20 22:05:41,840 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 8 rows
2024-07-20 22:05:45,557 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 29 rows
2024-07-20 22:06:19,170 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 114 rows
2024-07-20 22:06:19,797 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 4 rows
2024-07-20 22:06:23,150 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 33 rows
2024-07-20 22:06:24,388 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 13 rows
2024-07-20 22:06:26,889 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:06:32,141 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 52 rows
2024-07-20 22:06:48,978 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 85 rows
2024-07-20 22:06:51,081 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 28 rows
2024-07-20 22:06:55,645 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 68 rows
2024-07-20 22:07:05,677 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 102 rows
2024-07-20 22:07:09,786 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 18 rows
2024-07-20 22:07:13,700 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 26 rows
2024-07-20 22:07:15,741 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 9 rows
2024-07-20 22:07:43,943 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 97 rows
2024-07-20 22:07:45,550 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:07:46,472 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:07:55,735 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 44 rows
2024-07-20 22:08:02,030 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 53 rows
2024-07-20 22:08:09,447 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 65 rows
2024-07-20 22:08:09,757 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:08:12,510 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 16 rows
2024-07-20 22:08:14,830 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 15 rows
2024-07-20 22:08:15,992 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 13 rows
2024-07-20 22:08:27,321 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 120 rows
2024-07-20 22:08:32,053 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 52 rows
2024-07-20 22:08:32,534 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 6 rows
2024-07-20 22:08:36,400 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 53 rows
2024-07-20 22:08:41,717 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 42 rows
2024-07-20 22:10:10,307 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 13 rows
2024-07-20 22:11:01,544 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 412 rows
2024-07-20 22:11:05,706 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 42 rows
2024-07-20 22:11:11,889 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 30 rows
2024-07-20 22:11:13,822 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 21 rows
2024-07-20 22:11:18,435 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 70 rows
2024-07-20 22:11:44,072 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 72 rows
2024-07-20 22:12:17,910 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 79 rows
2024-07-20 22:12:24,510 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 124 rows
2024-07-20 22:12:28,842 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 54 rows
2024-07-20 22:12:33,927 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 33 rows
2024-07-20 22:12:34,230 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:12:39,162 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 35 rows
2024-07-20 22:12:43,233 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 43 rows
2024-07-20 22:12:48,477 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 69 rows
2024-07-20 22:12:48,761 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:12:49,259 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:12:53,552 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 42 rows
2024-07-20 22:12:59,981 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 21 rows
2024-07-20 22:13:05,583 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 56 rows
2024-07-20 22:13:08,273 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 14 rows
2024-07-20 22:13:08,576 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:13:09,080 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 8 rows
2024-07-20 22:13:11,547 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 28 rows
2024-07-20 22:13:12,229 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 8 rows
2024-07-20 22:13:17,313 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 61 rows
2024-07-20 22:13:20,331 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 44 rows
2024-07-20 22:13:20,842 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 7 rows
2024-07-20 22:13:21,470 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 8 rows
2024-07-20 22:13:36,683 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 235 rows
2024-07-20 22:13:39,090 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 41 rows
2024-07-20 22:13:42,505 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 48 rows
2024-07-20 22:14:05,542 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 105 rows
2024-07-20 22:14:10,935 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 38 rows
2024-07-20 22:14:14,097 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 38 rows
2024-07-20 22:14:23,969 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 44 rows
2024-07-20 22:15:04,747 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 179 rows
2024-07-20 22:15:08,105 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 34 rows
2024-07-20 22:15:09,686 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 3 rows
2024-07-20 22:15:10,462 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 16 rows
2024-07-20 22:15:17,345 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 125 rows
2024-07-20 22:15:26,391 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 19 rows
2024-07-20 22:15:26,852 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 5 rows
2024-07-20 22:17:30,851 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 1205 rows
2024-07-20 22:17:32,002 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 13 rows
2024-07-20 22:17:38,972 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 51 rows
2024-07-20 22:17:40,877 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 4 rows
2024-07-20 22:17:46,963 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 38 rows
2024-07-20 22:17:47,716 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 12 rows
2024-07-20 22:17:48,181 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 5 rows
2024-07-20 22:17:48,650 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 5 rows
2024-07-20 22:17:49,279 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 6 rows
2024-07-20 22:17:50,593 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 14 rows
2024-07-20 22:17:56,873 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 105 rows
2024-07-20 22:18:12,902 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 56 rows
2024-07-20 22:18:15,299 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 36 rows
2024-07-20 22:18:17,056 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 17 rows
2024-07-20 22:18:26,767 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 107 rows
2024-07-20 22:18:34,099 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 42 rows
2024-07-20 22:18:37,996 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 13 rows
2024-07-20 22:18:46,000 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 78 rows
2024-07-20 22:18:46,355 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:18:47,850 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 5 rows
2024-07-20 22:19:24,611 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-20 22:19:24.611591', 'RPC error': '2024-07-20 22:19:24.611780'}>
2024-07-20 22:19:24,612 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-20 22:19:29,817 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:19:31,095 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 18 rows
2024-07-20 22:19:31,556 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 8 rows
2024-07-20 22:19:41,829 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 109 rows
2024-07-20 22:19:42,172 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:19:51,421 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 106 rows
2024-07-20 22:19:54,825 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 64 rows
2024-07-20 22:19:57,957 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:19:58,471 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:20:36,267 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 77 rows
2024-07-20 22:20:36,754 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 4 rows
2024-07-20 22:20:38,199 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 16 rows
2024-07-20 22:20:38,542 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:21:18,191 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 65 rows
2024-07-20 22:21:32,078 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 3 rows
2024-07-20 22:21:32,951 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 12 rows
2024-07-20 22:21:34,245 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 16 rows
2024-07-20 22:21:41,850 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-20 22:21:41.849910', 'RPC error': '2024-07-20 22:21:41.850135'}>
2024-07-20 22:21:41,850 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-20 22:21:43,476 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 12 rows
2024-07-20 22:21:45,062 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 13 rows
2024-07-20 22:21:45,384 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:22:45,739 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 5 rows
2024-07-20 22:22:50,696 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 34 rows
2024-07-20 22:24:12,349 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 572 rows
2024-07-20 22:24:41,796 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 74 rows
2024-07-20 22:24:42,155 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:25:02,038 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 267 rows
2024-07-20 22:25:06,493 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 45 rows
2024-07-20 22:25:10,611 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 12 rows
2024-07-20 22:25:11,598 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 17 rows
2024-07-20 22:25:34,923 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 134 rows
2024-07-20 22:25:41,477 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 75 rows
2024-07-20 22:25:53,616 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 145 rows
2024-07-20 22:25:58,976 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 37 rows
2024-07-20 22:26:05,544 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 98 rows
2024-07-20 22:26:10,773 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 87 rows
2024-07-20 22:26:14,431 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 57 rows
2024-07-20 22:26:34,561 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 77 rows
2024-07-20 22:26:41,283 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 52 rows
2024-07-20 22:26:53,722 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 45 rows
2024-07-20 22:26:55,799 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 13 rows
2024-07-20 22:26:56,703 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 7 rows
2024-07-20 22:26:57,866 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 10 rows
2024-07-20 22:26:58,650 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 13 rows
2024-07-20 22:26:59,770 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 7 rows
2024-07-20 22:27:00,054 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:27:04,128 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 12 rows
2024-07-20 22:27:17,399 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:27:21,640 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 29 rows
2024-07-20 22:27:24,986 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 35 rows
2024-07-20 22:27:29,098 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 42 rows
2024-07-20 22:27:29,697 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 4 rows
2024-07-20 22:27:31,354 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 16 rows
2024-07-20 22:27:31,887 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 4 rows
2024-07-20 22:27:32,192 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:27:33,720 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 15 rows
2024-07-20 22:27:37,061 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 39 rows
2024-07-20 22:27:38,620 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 13 rows
2024-07-20 22:27:40,872 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 31 rows
2024-07-20 22:27:42,881 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 30 rows
2024-07-20 22:27:49,015 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 85 rows
2024-07-20 22:27:53,845 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 46 rows
2024-07-20 22:27:54,205 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 4 rows
2024-07-20 22:27:58,457 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 46 rows
2024-07-20 22:28:12,711 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 164 rows
2024-07-20 22:28:13,094 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:28:22,747 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 85 rows
2024-07-20 22:28:31,430 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 56 rows
2024-07-20 22:28:32,759 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:28:36,553 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 48 rows
2024-07-20 22:28:42,199 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 57 rows
2024-07-20 22:28:43,240 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 14 rows
2024-07-20 22:28:44,455 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 3 rows
2024-07-20 22:29:48,646 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 19 rows
2024-07-20 22:29:52,526 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 42 rows
2024-07-20 22:29:54,159 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 35 rows
2024-07-20 22:30:18,826 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 118 rows
2024-07-20 22:30:19,305 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:30:19,599 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:30:39,887 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 92 rows
2024-07-20 22:30:51,716 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 110 rows
2024-07-20 22:31:11,634 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 81 rows
2024-07-20 22:31:18,483 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 79 rows
2024-07-20 22:31:19,433 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 9 rows
2024-07-20 22:31:26,158 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 28 rows
2024-07-20 22:31:34,104 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 92 rows
2024-07-20 22:31:34,535 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 5 rows
2024-07-20 22:31:44,374 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 93 rows
2024-07-20 22:32:09,934 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 57 rows
2024-07-20 22:32:10,259 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:32:28,125 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 105 rows
2024-07-20 22:32:28,870 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 8 rows
2024-07-20 22:32:30,001 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:32:32,011 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 38 rows
2024-07-20 22:32:40,149 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 21 rows
2024-07-20 22:32:42,084 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 32 rows
2024-07-20 22:33:14,630 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 25 rows
2024-07-20 22:33:14,934 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:33:15,275 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 3 rows
2024-07-20 22:33:15,621 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 5 rows
2024-07-20 22:33:27,923 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 92 rows
2024-07-20 22:33:28,585 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 9 rows
2024-07-20 22:33:28,890 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:33:29,470 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 4 rows
2024-07-20 22:33:31,049 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 31 rows
2024-07-20 22:33:39,133 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 44 rows
2024-07-20 22:33:41,842 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 18 rows
2024-07-20 22:34:06,413 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:34:11,615 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-20 22:34:11.615582', 'RPC error': '2024-07-20 22:34:11.615762'}>
2024-07-20 22:34:11,616 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-20 22:34:14,686 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 19 rows
2024-07-20 22:34:15,043 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 5 rows
2024-07-20 22:34:24,844 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 38 rows
2024-07-20 22:34:25,898 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 17 rows
2024-07-20 22:34:33,178 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 31 rows
2024-07-20 22:34:35,744 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:34:42,851 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 44 rows
2024-07-20 22:35:16,813 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 152 rows
2024-07-20 22:35:22,335 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 6 rows
2024-07-20 22:35:48,760 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 106 rows
2024-07-20 22:35:52,388 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 47 rows
2024-07-20 22:35:55,130 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 41 rows
2024-07-20 22:35:56,129 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 3 rows
2024-07-20 22:35:57,151 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 13 rows
2024-07-20 22:36:15,894 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 65 rows
2024-07-20 22:36:16,689 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 6 rows
2024-07-20 22:36:30,039 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 112 rows
2024-07-20 22:36:58,158 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 108 rows
2024-07-20 22:36:59,994 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 29 rows
2024-07-20 22:37:10,256 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 62 rows
2024-07-20 22:37:11,688 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-20 22:37:11.688011', 'RPC error': '2024-07-20 22:37:11.688224'}>
2024-07-20 22:37:11,688 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-20 22:37:12,495 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 8 rows
2024-07-20 22:37:21,442 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 56 rows
2024-07-20 22:38:01,446 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 106 rows
2024-07-20 22:38:06,039 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 8 rows
2024-07-20 22:38:10,828 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 50 rows
2024-07-20 22:38:18,459 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 35 rows
2024-07-20 22:38:47,313 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 191 rows
2024-07-20 22:38:58,767 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 123 rows
2024-07-20 22:39:00,280 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 23 rows
2024-07-20 22:39:02,842 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 36 rows
2024-07-20 22:39:03,309 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 5 rows
2024-07-20 22:39:05,295 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 41 rows
2024-07-20 22:39:23,962 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 97 rows
2024-07-20 22:39:35,652 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 51 rows
2024-07-20 22:39:36,074 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 4 rows
2024-07-20 22:39:37,154 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 11 rows
2024-07-20 22:39:39,452 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 23 rows
2024-07-20 22:39:41,316 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 23 rows
2024-07-20 22:39:45,530 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 61 rows
2024-07-20 22:39:54,413 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 18 rows
2024-07-20 22:40:31,237 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 105 rows
2024-07-20 22:40:36,602 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 69 rows
2024-07-20 22:40:38,775 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 18 rows
2024-07-20 22:41:11,744 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 247 rows
2024-07-20 22:41:16,511 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 45 rows
2024-07-20 22:42:11,660 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 98 rows
2024-07-20 22:42:12,851 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 23 rows
2024-07-20 22:42:20,632 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 46 rows
2024-07-20 22:42:21,506 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 6 rows
2024-07-20 22:42:22,094 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 3 rows
2024-07-20 22:42:22,730 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 11 rows
2024-07-20 22:42:23,059 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:43:16,312 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 230 rows
2024-07-20 22:43:16,708 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:44:25,560 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 21 rows
2024-07-20 22:44:27,146 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 31 rows
2024-07-20 22:44:32,552 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 3 rows
2024-07-20 22:44:47,700 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 235 rows
2024-07-20 22:44:48,454 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 6 rows
2024-07-20 22:44:50,271 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 30 rows
2024-07-20 22:45:07,307 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 100 rows
2024-07-20 22:46:07,563 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 149 rows
2024-07-20 22:46:09,290 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 7 rows
2024-07-20 22:47:14,207 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 53 rows
2024-07-20 22:47:14,551 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:47:14,878 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:47:21,936 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 47 rows
2024-07-20 22:47:38,499 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 172 rows
2024-07-20 22:47:54,561 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 46 rows
2024-07-20 22:47:56,984 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 24 rows
2024-07-20 22:48:10,163 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 63 rows
2024-07-20 22:48:10,698 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 4 rows
2024-07-20 22:48:24,667 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 91 rows
2024-07-20 22:48:25,485 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 4 rows
2024-07-20 22:48:43,408 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 100 rows
2024-07-20 22:49:16,441 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 59 rows
2024-07-20 22:49:24,778 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 87 rows
2024-07-20 22:49:25,248 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:49:28,592 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 41 rows
2024-07-20 22:49:32,621 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 48 rows
2024-07-20 22:49:35,434 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 30 rows
2024-07-20 22:49:36,762 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:49:46,756 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 65 rows
2024-07-20 22:49:49,125 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 28 rows
2024-07-20 22:49:49,584 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 4 rows
2024-07-20 22:49:50,560 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:49:57,450 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 63 rows
2024-07-20 22:49:59,275 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 27 rows
2024-07-20 22:50:16,895 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 106 rows
2024-07-20 22:50:39,284 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 79 rows
2024-07-20 22:50:39,649 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:50:41,032 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:50:50,242 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 69 rows
2024-07-20 22:50:56,433 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 48 rows
2024-07-20 22:50:58,306 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 11 rows
2024-07-20 22:52:01,362 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 113 rows
2024-07-20 22:52:36,532 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 152 rows
2024-07-20 22:52:42,271 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 50 rows
2024-07-20 22:52:44,426 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 36 rows
2024-07-20 22:52:49,547 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 89 rows
2024-07-20 22:52:53,419 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 35 rows
2024-07-20 22:53:06,719 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 59 rows
2024-07-20 22:53:14,816 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 51 rows
2024-07-20 22:53:36,155 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 84 rows
2024-07-20 22:53:37,059 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 17 rows
2024-07-20 22:54:01,971 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 77 rows
2024-07-20 22:54:03,791 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 22 rows
2024-07-20 22:54:06,927 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:54:18,375 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 31 rows
2024-07-20 22:54:21,316 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 52 rows
2024-07-20 22:54:22,850 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 34 rows
2024-07-20 22:54:24,388 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 19 rows
2024-07-20 22:54:50,083 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 48 rows
2024-07-20 22:54:56,185 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 71 rows
2024-07-20 22:55:33,910 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 137 rows
2024-07-20 22:55:40,512 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 22:56:02,901 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 87 rows
2024-07-20 22:56:30,723 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 89 rows
2024-07-20 22:56:34,019 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 33 rows
2024-07-20 22:56:44,979 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 164 rows
2024-07-20 22:56:53,456 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 9 rows
2024-07-20 22:56:57,110 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 43 rows
2024-07-20 22:57:36,125 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 94 rows
2024-07-20 22:57:40,779 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 43 rows
2024-07-20 23:03:06,375 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 954 rows
2024-07-20 23:03:57,418 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 21 rows
2024-07-20 23:03:57,910 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 23:04:14,209 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 69 rows
2024-07-20 23:04:16,653 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 29 rows
2024-07-20 23:04:17,356 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 11 rows
2024-07-20 23:04:47,247 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 113 rows
2024-07-20 23:04:48,887 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 12 rows
2024-07-20 23:05:23,369 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 74 rows
2024-07-20 23:05:54,119 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 82 rows
2024-07-20 23:06:58,608 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 14 rows
2024-07-20 23:07:01,248 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 9 rows
2024-07-20 23:07:08,792 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 48 rows
2024-07-20 23:07:29,976 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 91 rows
2024-07-20 23:07:45,871 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 172 rows
2024-07-20 23:08:13,568 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 95 rows
2024-07-20 23:09:19,240 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 506 rows
2024-07-20 23:09:37,542 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 78 rows
2024-07-20 23:09:38,875 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 8 rows
2024-07-20 23:11:12,291 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 809 rows
2024-07-20 23:11:12,732 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 23:11:25,303 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 4 rows
2024-07-20 23:11:27,297 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 21 rows
2024-07-20 23:11:29,870 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 24 rows
2024-07-20 23:11:30,443 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 4 rows
2024-07-20 23:11:32,606 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 35 rows
2024-07-20 23:11:49,468 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 104 rows
2024-07-20 23:11:55,774 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 25 rows
2024-07-20 23:11:56,438 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 10 rows
2024-07-20 23:12:15,955 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 61 rows
2024-07-20 23:12:16,257 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 23:12:26,708 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 31 rows
2024-07-20 23:12:28,909 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 36 rows
2024-07-20 23:12:36,791 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 90 rows
2024-07-20 23:12:39,622 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 23 rows
2024-07-20 23:12:42,365 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 28 rows
2024-07-20 23:12:46,363 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 35 rows
2024-07-20 23:12:51,511 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 58 rows
2024-07-20 23:12:59,125 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 30 rows
2024-07-20 23:13:02,665 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 47 rows
2024-07-20 23:13:09,860 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 79 rows
2024-07-20 23:13:19,702 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 65 rows
2024-07-20 23:13:21,721 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 29 rows
2024-07-20 23:13:27,360 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 23:14:01,950 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 120 rows
2024-07-20 23:14:09,880 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 109 rows
2024-07-20 23:14:17,659 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 27 rows
2024-07-20 23:14:27,369 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 88 rows
2024-07-20 23:14:36,979 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 84 rows
2024-07-20 23:14:44,073 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 55 rows
2024-07-20 23:14:45,464 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 19 rows
2024-07-20 23:14:46,933 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 23:14:49,628 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 34 rows
2024-07-20 23:14:51,857 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 23:14:59,257 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 28 rows
2024-07-20 23:15:01,571 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 35 rows
2024-07-20 23:15:05,183 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 20 rows
2024-07-20 23:15:11,500 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 55 rows
2024-07-20 23:15:21,205 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 95 rows
2024-07-20 23:15:28,754 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 110 rows
2024-07-20 23:15:29,098 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 23:15:44,001 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 97 rows
2024-07-20 23:15:44,338 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 5 rows
2024-07-20 23:15:55,802 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 121 rows
2024-07-20 23:16:12,119 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 243 rows
2024-07-20 23:17:28,288 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 654 rows
2024-07-20 23:17:29,276 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 8 rows
2024-07-20 23:17:30,344 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 5 rows
2024-07-20 23:18:57,927 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 23:19:05,657 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 69 rows
2024-07-20 23:20:17,026 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 78 rows
2024-07-20 23:20:17,444 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 23:20:53,929 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 133 rows
2024-07-20 23:20:57,742 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 43 rows
2024-07-20 23:21:03,854 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 44 rows
2024-07-20 23:21:09,919 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 30 rows
2024-07-20 23:21:32,320 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 139 rows
2024-07-20 23:21:39,644 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 77 rows
2024-07-20 23:21:46,992 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 15 rows
2024-07-20 23:23:29,394 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 118 rows
2024-07-20 23:23:29,789 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 23:23:30,786 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 5 rows
2024-07-20 23:23:47,114 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 263 rows
2024-07-20 23:23:49,111 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 17 rows
2024-07-20 23:23:54,364 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 32 rows
2024-07-20 23:24:01,123 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 58 rows
2024-07-20 23:24:05,269 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 14 rows
2024-07-20 23:24:27,395 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 64 rows
2024-07-20 23:25:20,139 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 54 rows
2024-07-20 23:25:29,733 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 44 rows
2024-07-20 23:25:36,435 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-20 23:25:36.435738', 'RPC error': '2024-07-20 23:25:36.435897'}>
2024-07-20 23:25:36,436 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-20 23:25:37,302 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 8 rows
2024-07-20 23:25:37,937 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 8 rows
2024-07-20 23:25:48,771 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 87 rows
2024-07-20 23:26:02,596 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 68 rows
2024-07-20 23:26:50,014 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 409 rows
2024-07-20 23:26:57,083 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-20 23:26:57.083020', 'RPC error': '2024-07-20 23:26:57.083245'}>
2024-07-20 23:26:57,083 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-20 23:26:58,357 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 16 rows
2024-07-20 23:26:58,833 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 4 rows
2024-07-20 23:27:05,763 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 23:27:06,670 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 10 rows
2024-07-20 23:27:06,962 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 23:27:27,177 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 126 rows
2024-07-20 23:27:30,158 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 38 rows
2024-07-20 23:27:30,452 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 23:27:30,751 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 23:28:07,220 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 176 rows
2024-07-20 23:28:08,106 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 8 rows
2024-07-20 23:28:08,545 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 23:28:10,584 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 30 rows
2024-07-20 23:28:14,282 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 40 rows
2024-07-20 23:28:23,991 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 60 rows
2024-07-20 23:28:26,209 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 5 rows
2024-07-20 23:28:30,601 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 39 rows
2024-07-20 23:28:30,999 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 23:28:32,786 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 21 rows
2024-07-20 23:29:35,031 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 8 rows
2024-07-20 23:29:35,339 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 2 rows
2024-07-20 23:29:38,791 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 26 rows
2024-07-20 23:29:39,555 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0720 with 12 rows
2024-07-20 23:32:49,495 ｜ INFO ｜ milvus_helpers.py ｜ __init__ ｜ 14 ｜ Successfully connect to Milvus with IP:************* and PORT:19530
2024-07-20 23:32:49,593 ｜ INFO ｜ milvus_helpers.py ｜ create_collection ｜ 52 ｜ Create Milvus collection: productQA_qingxi_0721
2024-07-20 23:32:50,286 ｜ INFO ｜ milvus_helpers.py ｜ create_index ｜ 107 ｜ Successfully create index in collection:productQA_qingxi_0721 with param:{'index_type': 'IVF_FLAT', 'metric_type': 'COSINE', 'params': {'nlist': 16384}}
2024-07-20 23:32:54,258 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 19 rows
2024-07-20 23:32:58,288 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 46 rows
2024-07-20 23:32:59,875 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 21 rows
2024-07-20 23:33:08,044 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 71 rows
2024-07-20 23:33:39,132 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 69 rows
2024-07-20 23:33:42,292 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 29 rows
2024-07-20 23:33:43,037 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 8 rows
2024-07-20 23:33:47,064 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 29 rows
2024-07-20 23:34:00,856 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 46 rows
2024-07-20 23:34:26,471 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 149 rows
2024-07-20 23:34:49,324 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 114 rows
2024-07-20 23:34:49,984 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 4 rows
2024-07-20 23:34:53,512 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 33 rows
2024-07-20 23:34:54,803 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 13 rows
2024-07-20 23:34:57,332 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 2 rows
2024-07-20 23:35:02,518 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 52 rows
2024-07-20 23:35:15,682 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 85 rows
2024-07-20 23:35:17,979 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 28 rows
2024-07-20 23:35:23,405 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 68 rows
2024-07-20 23:35:34,108 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 102 rows
2024-07-20 23:35:38,305 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 18 rows
2024-07-20 23:35:41,538 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 26 rows
2024-07-20 23:35:43,416 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 9 rows
2024-07-20 23:36:09,070 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 97 rows
2024-07-20 23:36:18,299 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 19 rows
2024-07-20 23:36:18,691 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 2 rows
2024-07-20 23:36:19,730 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 2 rows
2024-07-20 23:36:27,784 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 44 rows
2024-07-20 23:36:33,796 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 53 rows
2024-07-20 23:36:41,870 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 65 rows
2024-07-20 23:36:42,185 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 2 rows
2024-07-20 23:36:44,303 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 16 rows
2024-07-20 23:36:46,672 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 15 rows
2024-07-20 23:36:47,944 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 13 rows
2024-07-20 23:36:59,031 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 120 rows
2024-07-20 23:37:03,534 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 52 rows
2024-07-20 23:37:04,019 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 6 rows
2024-07-20 23:37:08,077 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 53 rows
2024-07-20 23:37:13,345 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 42 rows
2024-07-20 23:38:48,931 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 13 rows
2024-07-20 23:39:34,559 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 412 rows
2024-07-20 23:39:38,283 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 42 rows
2024-07-20 23:39:43,376 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 30 rows
2024-07-20 23:39:45,785 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 21 rows
2024-07-20 23:39:50,024 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 70 rows
2024-07-20 23:40:15,363 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 72 rows
2024-07-20 23:40:44,741 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 79 rows
2024-07-20 23:40:52,433 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 124 rows
2024-07-20 23:40:56,815 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 54 rows
2024-07-20 23:41:17,554 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 30 rows
2024-07-20 23:41:21,203 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 33 rows
2024-07-20 23:41:21,519 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 2 rows
2024-07-20 23:41:26,667 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 35 rows
2024-07-20 23:41:30,798 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 43 rows
2024-07-20 23:41:36,903 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 69 rows
2024-07-20 23:41:37,198 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 2 rows
2024-07-20 23:41:37,717 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 2 rows
2024-07-20 23:41:42,431 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 42 rows
2024-07-20 23:41:49,350 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 21 rows
2024-07-20 23:41:55,265 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 56 rows
2024-07-20 23:41:57,637 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 14 rows
2024-07-20 23:41:58,085 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 2 rows
2024-07-20 23:41:58,772 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 8 rows
2024-07-20 23:42:01,358 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 28 rows
2024-07-20 23:42:02,321 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 8 rows
2024-07-20 23:42:07,792 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 61 rows
2024-07-20 23:42:11,050 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 44 rows
2024-07-20 23:42:11,753 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 7 rows
2024-07-20 23:42:12,564 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 8 rows
2024-07-20 23:42:29,918 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 235 rows
2024-07-20 23:42:32,713 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 41 rows
2024-07-20 23:42:37,689 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 48 rows
2024-07-20 23:42:57,372 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 105 rows
2024-07-20 23:43:02,337 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 38 rows
2024-07-20 23:43:05,490 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 38 rows
2024-07-20 23:43:12,303 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 44 rows
2024-07-20 23:43:45,076 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 179 rows
2024-07-20 23:43:48,825 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 34 rows
2024-07-20 23:43:50,570 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 3 rows
2024-07-20 23:43:51,776 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 16 rows
2024-07-20 23:43:54,228 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 23 rows
2024-07-20 23:44:02,819 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 125 rows
2024-07-20 23:44:09,149 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 19 rows
2024-07-20 23:44:09,610 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 5 rows
2024-07-20 23:46:12,542 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 1205 rows
2024-07-20 23:46:13,723 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 13 rows
2024-07-20 23:46:20,654 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 51 rows
2024-07-20 23:46:22,454 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 4 rows
2024-07-20 23:46:27,896 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 38 rows
2024-07-20 23:46:28,816 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 12 rows
2024-07-20 23:46:29,305 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 5 rows
2024-07-20 23:46:29,834 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 5 rows
2024-07-20 23:46:30,472 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 6 rows
2024-07-20 23:46:31,946 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 14 rows
2024-07-20 23:46:39,374 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 105 rows
2024-07-20 23:46:51,789 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 56 rows
2024-07-20 23:46:54,418 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 36 rows
2024-07-20 23:46:55,903 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 17 rows
2024-07-20 23:47:05,317 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 107 rows
2024-07-20 23:47:14,097 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 42 rows
2024-07-20 23:47:21,517 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 22 rows
2024-07-20 23:48:15,607 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 150 rows
2024-07-20 23:48:16,607 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 13 rows
2024-07-20 23:48:33,202 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 21 rows
2024-07-20 23:48:40,007 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 78 rows
2024-07-20 23:48:40,401 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 2 rows
2024-07-20 23:48:42,944 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 5 rows
2024-07-20 23:49:10,337 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 108 rows
2024-07-20 23:49:15,556 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 2 rows
2024-07-20 23:49:17,010 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 18 rows
2024-07-20 23:49:17,605 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 8 rows
2024-07-20 23:49:22,909 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 5 rows
2024-07-20 23:49:33,793 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 109 rows
2024-07-20 23:49:34,098 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 2 rows
2024-07-20 23:49:43,504 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 106 rows
2024-07-20 23:49:47,656 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 64 rows
2024-07-20 23:49:50,720 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 2 rows
2024-07-20 23:49:51,096 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 2 rows
2024-07-20 23:50:22,663 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 77 rows
2024-07-20 23:50:23,256 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 4 rows
2024-07-20 23:50:24,913 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 16 rows
2024-07-20 23:50:25,237 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 2 rows
2024-07-20 23:50:53,740 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 65 rows
2024-07-20 23:51:07,598 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 3 rows
2024-07-20 23:51:08,526 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 12 rows
2024-07-20 23:51:09,926 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 16 rows
2024-07-20 23:51:15,967 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 33 rows
2024-07-20 23:51:17,531 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 12 rows
2024-07-20 23:51:19,387 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 13 rows
2024-07-20 23:51:19,698 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 2 rows
2024-07-20 23:52:03,666 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 5 rows
2024-07-20 23:52:08,356 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 34 rows
2024-07-20 23:53:21,978 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 572 rows
2024-07-20 23:53:46,695 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 74 rows
2024-07-20 23:53:46,997 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 2 rows
2024-07-20 23:54:09,395 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 267 rows
2024-07-20 23:54:13,711 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 45 rows
2024-07-20 23:54:16,973 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 12 rows
2024-07-20 23:54:18,622 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 17 rows
2024-07-20 23:54:41,926 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 134 rows
2024-07-20 23:54:48,353 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 75 rows
2024-07-20 23:55:00,237 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 145 rows
2024-07-20 23:55:05,374 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 37 rows
2024-07-20 23:55:12,427 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 98 rows
2024-07-20 23:55:18,459 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 87 rows
2024-07-20 23:55:22,365 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 57 rows
2024-07-20 23:55:47,723 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 77 rows
2024-07-20 23:55:53,563 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 52 rows
2024-07-20 23:56:02,659 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 45 rows
2024-07-20 23:56:05,071 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 13 rows
2024-07-20 23:56:05,880 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 7 rows
2024-07-20 23:56:06,951 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 10 rows
2024-07-20 23:56:07,884 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 13 rows
2024-07-20 23:56:09,185 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 7 rows
2024-07-20 23:56:09,477 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 2 rows
2024-07-20 23:56:12,288 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 12 rows
2024-07-20 23:56:25,288 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 2 rows
2024-07-20 23:56:28,917 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 29 rows
2024-07-20 23:56:32,540 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 35 rows
2024-07-20 23:56:37,463 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 42 rows
2024-07-20 23:56:37,987 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 4 rows
2024-07-20 23:56:39,704 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 16 rows
2024-07-20 23:56:40,219 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 4 rows
2024-07-20 23:56:40,521 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 2 rows
2024-07-20 23:56:42,163 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 15 rows
2024-07-20 23:56:45,301 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 39 rows
2024-07-20 23:56:46,883 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 13 rows
2024-07-20 23:56:49,312 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 31 rows
2024-07-20 23:56:51,769 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 30 rows
2024-07-20 23:56:58,279 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 85 rows
2024-07-20 23:57:02,701 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 46 rows
2024-07-20 23:57:03,190 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 4 rows
2024-07-20 23:57:07,720 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 46 rows
2024-07-20 23:57:22,554 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 164 rows
2024-07-20 23:57:23,009 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 2 rows
2024-07-20 23:57:32,843 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 85 rows
2024-07-20 23:57:42,209 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 56 rows
2024-07-20 23:57:48,820 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 11 rows
2024-07-20 23:57:49,165 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 2 rows
2024-07-20 23:57:53,127 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 48 rows
2024-07-20 23:57:59,064 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 57 rows
2024-07-20 23:58:00,249 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 14 rows
2024-07-20 23:58:01,352 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi_0721 with 3 rows
