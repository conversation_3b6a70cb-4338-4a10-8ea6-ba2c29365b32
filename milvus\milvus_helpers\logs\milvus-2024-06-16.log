2024-06-16 15:27:25,907 ｜ INFO ｜ milvus_helpers.py ｜ __init__ ｜ 14 ｜ Successfully connect to Milvus with IP:************* and PORT:19530
2024-06-16 15:27:27,652 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-16 15:27:28,597 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-16 15:27:30,119 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-16 15:27:31,222 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-16 15:27:32,711 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-16 15:27:34,111 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-16 15:27:36,988 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 20 rows
2024-06-16 15:27:38,650 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 10 rows
2024-06-16 15:27:39,633 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-16 15:27:41,104 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-16 15:27:42,483 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-16 15:27:43,569 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-16 15:27:45,201 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-16 15:27:46,305 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-16 15:27:47,500 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-16 15:27:48,503 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-16 15:27:50,063 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 10 rows
2024-06-16 15:27:51,678 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 10 rows
2024-06-16 15:27:52,992 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-16 15:27:55,363 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 14 rows
2024-06-16 15:27:56,441 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-16 15:27:57,869 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-16 15:27:58,803 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-16 15:28:00,917 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 14 rows
2024-06-16 15:28:02,178 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-16 15:28:03,857 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 11 rows
2024-06-16 15:28:05,805 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 13 rows
2024-06-16 15:28:06,975 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-16 15:28:08,942 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 12 rows
2024-06-16 15:28:10,401 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-16 15:28:11,652 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-16 15:28:12,867 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-16 15:28:14,370 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-16 15:28:16,500 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 16 rows
2024-06-16 15:28:18,202 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 12 rows
2024-06-16 15:28:19,559 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-16 15:28:21,118 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 10 rows
2024-06-16 15:28:22,026 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-16 15:28:23,580 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-16 15:28:24,541 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-16 15:28:25,831 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-16 15:28:26,895 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-16 15:28:27,984 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-16 15:28:30,170 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 14 rows
2024-06-16 15:28:31,442 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-16 15:28:33,535 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 13 rows
2024-06-16 15:28:35,012 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-16 15:28:36,330 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-16 15:28:37,652 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-16 15:28:39,319 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 11 rows
2024-06-16 15:28:40,898 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-16 15:28:42,255 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-16 15:28:43,560 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-16 15:28:44,845 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-16 15:28:46,863 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 13 rows
2024-06-16 15:28:48,046 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-16 15:28:49,314 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-16 15:28:50,944 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 12 rows
2024-06-16 15:28:52,062 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-16 15:28:53,126 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-16 15:28:54,809 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-16 15:28:55,918 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-16 15:28:57,557 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 10 rows
2024-06-16 15:30:35,632 ｜ INFO ｜ milvus_helpers.py ｜ __init__ ｜ 14 ｜ Successfully connect to Milvus with IP:************* and PORT:19530
2024-06-16 15:30:35,767 ｜ INFO ｜ milvus_helpers.py ｜ create_collection ｜ 52 ｜ Create Milvus collection: productQA_yxxbk_test
2024-06-16 15:30:36,438 ｜ INFO ｜ milvus_helpers.py ｜ create_index ｜ 107 ｜ Successfully create index in collection:productQA_yxxbk_test with param:{'index_type': 'IVF_FLAT', 'metric_type': 'COSINE', 'params': {'nlist': 16384}}
2024-06-16 15:30:39,740 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:30:41,115 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:30:42,151 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:30:43,013 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:30:44,710 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-16 15:30:45,719 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:30:46,814 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:30:48,375 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:30:49,969 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-16 15:30:50,952 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:30:52,008 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:30:53,517 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-16 15:30:54,326 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:30:55,538 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:30:56,958 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:30:59,065 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-16 15:31:00,045 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:31:01,347 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:31:02,877 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:31:05,946 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-16 15:31:07,870 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-16 15:31:08,951 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:31:09,968 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:31:11,832 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-16 15:31:13,505 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-16 15:31:14,369 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:31:15,174 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:31:16,105 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:31:17,080 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:31:18,615 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-16 15:31:20,566 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-16 15:31:21,836 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:31:22,820 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:31:23,823 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:31:25,241 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:31:26,691 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-16 15:31:27,607 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:31:28,940 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:31:29,994 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:31:30,854 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:31:32,228 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:31:33,344 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:31:34,988 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:31:36,220 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:31:39,742 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 25 rows
2024-06-16 15:31:41,042 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:31:42,305 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:31:44,662 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-16 15:31:45,927 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:31:47,296 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:31:48,496 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:31:50,116 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-16 15:31:51,784 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-16 15:31:52,752 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:31:53,772 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:31:54,727 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:31:55,623 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:31:57,125 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-16 15:31:58,269 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:31:59,555 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:32:00,703 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:32:01,643 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:32:02,509 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:32:03,588 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:32:05,848 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-16 15:32:06,771 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:32:07,918 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:32:09,007 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:32:10,301 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:32:11,675 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:32:12,622 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:32:14,286 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-16 15:32:15,860 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-16 15:32:17,125 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:32:19,096 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-16 15:32:20,338 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:32:22,380 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-16 15:32:23,396 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:32:24,701 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:32:25,769 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:32:27,692 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-16 15:32:29,565 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-16 15:32:30,416 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:32:31,735 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:32:33,814 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-16 15:32:35,013 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:32:36,656 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-16 15:32:38,610 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-16 15:32:40,130 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-16 15:32:41,510 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:32:43,171 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-16 15:32:44,460 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:32:45,723 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:32:46,898 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:32:48,022 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:32:50,008 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-16 15:32:51,239 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:32:52,806 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-16 15:32:53,808 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:32:55,868 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-16 15:32:57,659 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-16 15:32:59,328 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-16 15:33:01,023 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-16 15:33:02,259 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:33:03,655 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:33:04,846 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:33:06,162 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:33:07,411 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:33:08,488 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:33:10,237 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-16 15:33:11,118 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:33:11,890 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:33:12,908 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:33:14,194 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:33:15,628 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:33:16,442 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:33:19,053 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-16 15:33:20,941 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-16 15:33:21,854 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:33:23,430 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-16 15:33:24,787 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:33:26,573 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-16 15:33:27,681 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:33:29,339 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-16 15:33:31,061 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-16 15:33:32,383 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:33:33,165 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:33:34,453 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:33:36,167 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-16 15:33:37,206 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:33:38,351 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:33:39,409 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:33:40,338 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:33:41,910 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-16 15:33:42,885 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:33:44,250 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:33:45,287 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:33:47,933 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 18 rows
2024-06-16 15:33:48,835 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:33:49,792 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:33:51,092 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:33:52,497 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:33:53,968 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:33:54,755 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:33:55,758 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:33:56,584 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:33:57,662 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:33:58,540 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:33:59,577 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:34:00,561 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:34:01,490 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:34:02,810 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:34:04,315 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-16 15:34:05,660 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:34:06,398 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:34:07,262 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:34:08,543 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:34:09,523 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:34:10,370 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:34:11,698 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:34:13,425 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-16 15:34:14,399 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:34:16,672 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-16 15:34:17,956 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:34:18,903 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:34:19,917 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:34:20,848 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:34:22,226 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-16 15:34:23,652 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:34:25,631 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-16 15:34:27,723 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-16 15:34:29,193 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-16 15:34:30,253 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:34:31,647 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:34:32,553 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:34:33,851 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:34:35,146 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:34:36,477 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:34:37,715 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:34:38,662 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:34:39,557 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:34:40,378 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:34:41,261 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:34:42,201 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:34:43,198 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:34:44,108 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:34:45,162 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:34:47,200 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-16 15:34:48,485 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:34:50,045 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-16 15:34:51,178 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:34:52,627 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:34:53,655 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:34:54,905 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:34:56,189 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:34:57,132 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:34:58,771 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-16 15:35:00,332 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-16 15:35:01,679 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:35:03,278 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-16 15:35:05,267 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-16 15:35:06,644 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:35:07,771 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:35:08,789 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:35:10,027 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:35:11,121 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:35:11,987 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:35:13,996 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-16 15:35:14,978 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:35:15,859 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:35:17,000 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:35:19,153 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-16 15:35:20,376 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:35:21,676 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:35:23,420 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-16 15:35:24,251 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:35:26,520 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-16 15:35:27,571 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:35:28,552 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:35:29,562 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:35:33,039 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 25 rows
2024-06-16 15:35:33,783 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:35:35,319 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-16 15:35:36,080 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:35:37,038 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:35:38,583 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:35:39,675 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:35:41,203 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:35:43,468 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-16 15:35:45,216 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-16 15:35:46,110 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:35:47,013 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:35:48,447 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-16 15:35:49,625 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:35:50,864 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:35:51,888 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:35:52,895 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:35:53,826 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:35:54,801 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:35:56,376 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-16 15:35:57,343 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:35:58,300 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:36:00,118 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:36:00,919 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:36:01,725 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:36:02,641 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:36:03,702 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:36:04,852 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-16 15:36:05,839 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-16 15:36:07,129 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:36:09,216 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-16 15:36:10,027 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-16 15:36:11,348 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-16 15:36:12,653 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-16 15:36:14,328 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-16 15:36:15,893 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-16 15:36:16,885 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
