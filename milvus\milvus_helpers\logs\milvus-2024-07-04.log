2024-07-04 00:00:08,253 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 123 rows
2024-07-04 00:00:28,574 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 192 rows
2024-07-04 00:00:30,823 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 00:00:56,442 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 113 rows
2024-07-04 00:00:58,149 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 00:00:59,726 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 00:01:12,690 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 00:01:28,479 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 89 rows
2024-07-04 00:02:31,909 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 147 rows
2024-07-04 00:04:10,762 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 00:04:34,725 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 154 rows
2024-07-04 00:04:36,631 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 00:04:42,082 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 00:05:05,029 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 230 rows
2024-07-04 00:06:44,459 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 75 rows
2024-07-04 00:06:48,046 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 00:07:04,973 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 62 rows
2024-07-04 00:07:17,394 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 123 rows
2024-07-04 00:07:21,883 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 00:07:27,569 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 00:07:40,332 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 00:07:42,292 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 00:08:24,396 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 79 rows
2024-07-04 00:08:34,906 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 00:11:18,483 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 57 rows
2024-07-04 00:11:32,693 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 00:12:27,561 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 171 rows
2024-07-04 00:13:00,966 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 118 rows
2024-07-04 00:13:33,889 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 216 rows
2024-07-04 00:13:34,656 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 00:13:48,936 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 118 rows
2024-07-04 00:14:35,966 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 00:14:40,525 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 00:14:41,298 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 00:15:01,734 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-04 00:15:04,381 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 00:15:16,548 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 00:15:17,938 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 14 rows
2024-07-04 00:15:27,770 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 148 rows
2024-07-04 00:15:38,350 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 105 rows
2024-07-04 00:16:21,342 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 79 rows
2024-07-04 00:16:26,438 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 00:16:43,149 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 164 rows
2024-07-04 00:17:40,026 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 141 rows
2024-07-04 00:17:52,775 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 100 rows
2024-07-04 00:17:58,439 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 00:18:18,368 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 161 rows
2024-07-04 00:18:22,237 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-04 00:18:24,446 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 00:18:30,859 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 00:18:54,795 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 152 rows
2024-07-04 00:18:58,211 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 00:18:59,938 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 00:19:18,837 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 40 rows
2024-07-04 00:21:25,132 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 00:21:37,589 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 00:21:45,644 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 00:23:58,140 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 00:23:59,243 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 00:24:19,346 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 00:24:51,787 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 00:24:58,370 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 00:25:27,868 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 170 rows
2024-07-04 00:25:39,557 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 00:26:11,004 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 00:26:17,104 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 53 rows
2024-07-04 00:27:35,339 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 00:27:48,813 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 110 rows
2024-07-04 00:28:03,812 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 00:28:08,435 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 00:28:10,449 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 00:28:23,092 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-04 00:28:52,121 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 116 rows
2024-07-04 00:28:57,117 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 00:29:21,657 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 56 rows
2024-07-04 00:29:31,826 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 10 rows
2024-07-04 00:29:37,351 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 43 rows
2024-07-04 00:29:56,699 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 55 rows
2024-07-04 00:30:06,948 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 79 rows
2024-07-04 00:30:30,024 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 166 rows
2024-07-04 00:30:33,005 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 40 rows
2024-07-04 00:30:33,385 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 00:31:05,171 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 117 rows
2024-07-04 00:31:42,193 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-04 00:31:47,751 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 00:31:59,626 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 118 rows
2024-07-04 00:32:28,840 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 106 rows
2024-07-04 00:32:32,762 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 00:33:09,961 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-04 00:33:15,682 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 25 rows
2024-07-04 00:33:20,575 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-04 00:33:28,881 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 77 rows
2024-07-04 00:33:36,892 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 00:35:01,542 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 10 rows
2024-07-04 00:35:02,300 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 00:35:07,338 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 40 rows
2024-07-04 00:35:13,063 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 00:35:15,756 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 00:35:34,979 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 64 rows
2024-07-04 00:35:45,794 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 00:35:46,469 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 00:35:56,569 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 00:36:29,414 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 122 rows
2024-07-04 00:36:34,041 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 00:36:40,810 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 25 rows
2024-07-04 00:36:43,171 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-04 00:36:50,760 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 00:37:23,011 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 00:37:23,797 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 00:40:02,442 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1269 rows
2024-07-04 00:40:10,943 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 74 rows
2024-07-04 00:40:31,418 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 68 rows
2024-07-04 00:40:31,986 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 00:40:38,001 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 00:40:49,664 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 00:41:24,334 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 00:43:25,704 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 00:43:30,402 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 70 rows
2024-07-04 00:43:55,293 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 62 rows
2024-07-04 00:44:47,695 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 00:47:16,528 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 82 rows
2024-07-04 00:47:20,837 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-04 00:47:53,961 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 133 rows
2024-07-04 00:47:54,739 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1 rows
2024-07-04 00:47:55,454 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 00:47:58,535 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 00:48:07,183 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 64 rows
2024-07-04 00:48:10,587 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 00:48:12,757 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 00:48:14,331 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 00:48:38,680 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 122 rows
2024-07-04 00:50:24,771 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 91 rows
2024-07-04 00:50:46,877 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 76 rows
2024-07-04 00:50:49,851 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 00:50:50,708 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 00:50:52,282 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 00:51:05,756 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 82 rows
2024-07-04 00:51:22,446 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 00:51:46,923 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 53 rows
2024-07-04 00:52:01,991 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 58 rows
2024-07-04 00:52:35,023 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 89 rows
2024-07-04 00:52:58,098 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-04 00:53:02,926 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 00:53:05,765 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 00:53:06,465 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 00:53:07,234 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 00:53:09,912 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 00:53:24,981 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 00:53:27,616 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 00:54:32,974 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 00:54:59,018 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 147 rows
2024-07-04 00:55:06,305 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 00:56:15,590 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 130 rows
2024-07-04 00:56:29,550 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 121 rows
2024-07-04 00:56:37,072 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 00:56:52,941 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 00:57:01,731 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 00:57:34,605 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 57 rows
2024-07-04 00:57:40,203 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 00:58:13,993 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 107 rows
2024-07-04 00:58:25,266 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 54 rows
2024-07-04 00:58:26,679 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 00:59:02,309 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 138 rows
2024-07-04 00:59:04,481 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 00:59:15,401 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 01:00:00,405 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 01:00:09,214 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 01:00:15,231 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 01:00:30,633 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 01:01:13,498 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 83 rows
2024-07-04 01:01:23,573 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 51 rows
2024-07-04 01:01:30,169 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-04 01:02:24,546 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-04 01:02:30,148 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 01:02:30,616 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 01:02:36,679 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 01:03:13,118 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 155 rows
2024-07-04 01:03:17,835 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 01:03:58,996 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 01:04:08,155 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 01:04:14,795 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 01:04:24,678 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 69 rows
2024-07-04 01:04:26,745 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-04 01:04:39,663 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 54 rows
2024-07-04 01:05:08,625 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 72 rows
2024-07-04 01:05:09,192 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 01:05:23,911 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 01:05:28,478 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 01:05:56,567 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 01:06:49,435 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 254 rows
2024-07-04 01:06:50,003 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 01:06:52,260 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 01:08:17,351 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 01:08:22,573 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 01:08:26,666 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 01:08:27,503 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 01:08:31,951 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 01:11:09,322 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 62 rows
2024-07-04 01:11:17,745 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 01:11:48,612 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 01:11:49,370 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 01:11:50,888 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 01:11:54,488 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 01:12:00,977 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 01:12:42,741 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 338 rows
2024-07-04 01:12:48,765 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 01:13:23,604 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 114 rows
2024-07-04 01:13:33,085 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 01:13:38,094 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 10 rows
2024-07-04 01:13:50,079 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 51 rows
2024-07-04 01:13:53,601 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 01:13:57,510 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 01:14:38,144 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 153 rows
2024-07-04 01:15:25,155 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 43 rows
2024-07-04 01:16:31,625 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 114 rows
2024-07-04 01:17:18,048 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 254 rows
2024-07-04 01:17:45,059 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 52 rows
2024-07-04 01:17:47,058 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 01:17:52,066 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 01:18:06,148 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 125 rows
2024-07-04 01:18:22,569 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 25 rows
2024-07-04 01:18:50,944 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 01:19:11,470 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 62 rows
2024-07-04 01:19:21,572 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 25 rows
2024-07-04 01:19:24,302 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-04 01:19:25,247 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 01:19:35,930 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-04 01:19:50,360 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 01:19:51,249 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 01:19:58,566 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 56 rows
2024-07-04 01:20:08,420 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 71 rows
2024-07-04 01:20:14,064 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 01:20:19,266 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 01:20:21,800 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 01:21:17,453 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-04 01:21:24,968 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-04 01:21:59,971 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 165 rows
2024-07-04 01:22:01,163 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 01:22:17,360 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 86 rows
2024-07-04 01:22:35,643 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 01:22:36,433 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 01:23:10,120 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 61 rows
2024-07-04 01:23:23,976 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 58 rows
2024-07-04 01:23:33,612 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 72 rows
2024-07-04 01:23:36,486 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-04 01:23:49,464 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 132 rows
2024-07-04 01:23:51,615 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 01:25:30,633 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 49 rows
2024-07-04 01:25:32,697 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 01:26:05,621 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 01:26:52,915 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 01:27:47,306 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 56 rows
2024-07-04 01:28:49,044 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 01:29:08,950 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 128 rows
2024-07-04 01:29:14,226 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 01:29:18,426 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-04 01:30:15,807 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 118 rows
2024-07-04 01:31:36,451 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 267 rows
2024-07-04 01:31:37,157 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 01:31:46,099 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 01:32:05,745 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 134 rows
2024-07-04 01:33:25,667 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 89 rows
2024-07-04 01:34:42,751 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 68 rows
2024-07-04 01:34:44,102 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 01:34:54,672 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 69 rows
2024-07-04 01:34:57,657 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 01:35:10,145 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 107 rows
2024-07-04 01:35:30,855 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 01:35:33,254 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 01:35:35,662 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 01:37:00,136 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 654 rows
2024-07-04 01:37:12,091 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-04 01:37:13,436 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 01:37:23,872 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 77 rows
2024-07-04 01:37:31,106 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 68 rows
2024-07-04 01:38:30,105 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 75 rows
2024-07-04 01:38:31,971 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 01:38:43,113 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 75 rows
2024-07-04 01:39:03,193 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 01:39:06,461 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-04 01:39:56,824 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 108 rows
2024-07-04 01:40:02,951 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 01:40:07,230 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 01:40:09,984 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 01:40:23,516 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 124 rows
2024-07-04 01:40:24,229 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 01:40:26,753 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 01:40:42,623 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 01:40:46,063 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 01:41:14,546 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 71 rows
2024-07-04 01:41:17,663 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 01:41:40,469 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 01:41:54,628 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 88 rows
2024-07-04 01:42:34,725 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 54 rows
2024-07-04 01:42:44,160 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 110 rows
2024-07-04 01:42:45,242 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 01:43:30,494 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 197 rows
2024-07-04 01:44:29,758 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 203 rows
2024-07-04 01:44:31,202 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 01:44:34,572 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 25 rows
2024-07-04 01:45:27,077 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 01:46:08,860 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 01:46:42,076 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 96 rows
2024-07-04 01:46:46,931 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 01:47:26,164 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 166 rows
2024-07-04 01:47:32,787 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 01:47:45,800 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 01:47:48,307 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 01:47:59,254 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-04 01:48:03,112 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 01:48:45,515 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 01:49:51,055 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 167 rows
2024-07-04 01:49:54,472 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 01:50:33,098 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 01:50:38,989 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 01:50:40,844 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 01:50:52,736 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 01:50:58,996 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 01:51:18,484 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 71 rows
2024-07-04 01:51:33,226 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 127 rows
2024-07-04 01:51:34,874 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 01:51:41,478 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 01:51:53,270 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 111 rows
2024-07-04 01:52:01,630 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 01:52:03,236 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 01:52:07,670 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 01:52:30,948 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 76 rows
2024-07-04 01:52:31,825 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 01:52:43,905 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-04 01:52:46,595 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 01:53:40,598 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 01:53:41,416 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 01:54:56,198 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 98 rows
2024-07-04 01:55:01,065 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 01:55:09,727 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 58 rows
2024-07-04 01:55:13,086 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 01:55:34,722 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 65 rows
2024-07-04 01:55:37,480 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 02:00:28,928 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1220 rows
2024-07-04 02:00:34,293 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 02:01:00,622 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 02:01:06,391 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 43 rows
2024-07-04 02:03:40,508 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 14 rows
2024-07-04 02:03:51,012 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 88 rows
2024-07-04 02:03:59,778 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 02:04:00,791 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 02:04:01,819 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 02:04:10,862 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 02:04:15,756 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 02:05:29,875 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 526 rows
2024-07-04 02:05:39,024 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 02:05:51,322 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 94 rows
2024-07-04 02:05:59,031 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 02:06:04,782 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 02:06:11,190 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 40 rows
2024-07-04 02:06:25,164 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 02:07:02,769 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 02:07:07,286 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 02:07:23,876 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 132 rows
2024-07-04 02:08:05,010 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 83 rows
2024-07-04 02:08:13,098 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-04 02:08:37,243 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 139 rows
2024-07-04 02:09:11,802 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 136 rows
2024-07-04 02:09:38,707 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 64 rows
2024-07-04 02:11:46,605 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 72 rows
2024-07-04 02:13:47,334 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-04 02:13:47,732 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 02:13:48,857 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 02:13:51,136 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 02:14:07,843 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 76 rows
2024-07-04 02:14:26,408 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 132 rows
2024-07-04 02:14:28,515 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 02:14:50,483 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 61 rows
2024-07-04 02:14:59,486 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 02:15:05,521 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 02:15:12,549 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 02:15:30,558 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 65 rows
2024-07-04 02:15:34,993 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 25 rows
2024-07-04 02:18:12,896 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1413 rows
2024-07-04 02:20:49,125 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 92 rows
2024-07-04 02:20:55,895 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 02:23:33,723 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1141 rows
2024-07-04 02:23:34,292 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 02:23:34,964 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 02:25:19,994 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 62 rows
2024-07-04 02:25:26,029 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 02:25:49,240 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 61 rows
2024-07-04 02:27:17,402 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 917 rows
2024-07-04 02:30:42,594 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 02:30:53,203 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 117 rows
2024-07-04 02:31:14,151 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 127 rows
2024-07-04 02:31:45,980 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 100 rows
2024-07-04 02:31:58,575 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 02:32:04,462 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 02:33:39,830 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-04 02:35:06,634 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 02:35:14,339 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 02:35:46,027 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 02:35:48,907 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 02:36:02,212 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 90 rows
2024-07-04 02:36:15,608 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 56 rows
2024-07-04 02:37:22,261 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 178 rows
2024-07-04 02:37:29,025 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 02:37:58,113 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 02:37:59,112 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 02:38:01,513 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 02:38:12,564 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 02:38:18,403 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 02:38:24,968 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 02:39:32,459 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 02:39:41,611 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 106 rows
2024-07-04 02:40:23,927 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 99 rows
2024-07-04 02:40:34,138 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 113 rows
2024-07-04 02:40:38,540 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 55 rows
2024-07-04 02:40:43,005 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 02:40:45,663 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 02:41:35,907 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 80 rows
2024-07-04 02:41:36,401 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 02:41:39,153 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 14 rows
2024-07-04 02:41:58,739 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 145 rows
2024-07-04 02:42:17,359 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 100 rows
2024-07-04 02:42:52,461 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 298 rows
2024-07-04 02:42:55,366 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 02:42:58,476 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 02:43:07,474 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 02:43:13,339 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 02:45:02,572 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1058 rows
2024-07-04 02:46:53,390 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 02:46:54,229 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 02:47:13,075 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 43 rows
2024-07-04 02:47:22,011 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 66 rows
2024-07-04 02:47:36,577 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 131 rows
2024-07-04 02:47:48,654 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 89 rows
2024-07-04 02:49:01,649 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 02:49:03,174 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 02:49:25,947 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 76 rows
2024-07-04 02:49:50,702 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 136 rows
2024-07-04 02:50:10,772 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 56 rows
2024-07-04 02:50:19,841 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-04 02:51:29,219 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 183 rows
2024-07-04 02:51:38,766 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 02:52:03,352 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 80 rows
2024-07-04 02:52:04,210 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 02:52:13,082 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 70 rows
2024-07-04 02:52:14,010 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 02:52:18,457 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 02:52:28,365 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 112 rows
2024-07-04 02:52:37,453 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 71 rows
2024-07-04 02:52:37,995 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 02:53:51,791 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 649 rows
2024-07-04 02:53:55,015 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 10 rows
2024-07-04 02:54:00,935 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 02:54:02,921 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 02:54:05,218 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 02:54:22,821 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 97 rows
2024-07-04 02:54:23,520 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 02:54:45,699 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 02:58:39,898 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 118 rows
2024-07-04 03:00:08,692 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 61 rows
2024-07-04 03:01:02,139 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 03:01:03,096 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 10 rows
2024-07-04 03:01:12,498 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 93 rows
2024-07-04 03:01:24,321 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 80 rows
2024-07-04 03:01:41,171 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 110 rows
2024-07-04 03:01:45,670 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 53 rows
2024-07-04 03:02:09,472 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 58 rows
2024-07-04 03:03:24,422 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 93 rows
2024-07-04 03:03:26,662 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 03:03:37,633 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 82 rows
2024-07-04 03:03:52,465 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 115 rows
2024-07-04 03:03:54,515 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 03:03:56,123 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 10 rows
2024-07-04 03:05:25,682 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 03:05:50,333 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 03:06:07,110 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 88 rows
2024-07-04 03:06:14,770 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-04 03:07:32,549 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 516 rows
2024-07-04 03:07:47,050 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 55 rows
2024-07-04 03:08:38,587 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 03:08:49,446 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 72 rows
2024-07-04 03:08:54,805 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 25 rows
2024-07-04 03:09:22,460 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 79 rows
2024-07-04 03:09:47,640 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 76 rows
2024-07-04 03:09:50,753 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 03:10:09,047 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 96 rows
2024-07-04 03:10:23,482 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-04 03:10:28,559 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 03:10:32,598 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 03:12:31,212 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 03:12:39,682 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-04 03:12:42,275 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 03:15:42,769 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1952 rows
2024-07-04 03:15:45,846 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 03:15:49,775 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 03:16:02,802 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 121 rows
2024-07-04 03:16:12,366 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 80 rows
2024-07-04 03:16:34,280 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 124 rows
2024-07-04 03:17:04,921 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 104 rows
2024-07-04 03:17:10,856 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 03:17:25,827 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 136 rows
2024-07-04 03:17:26,664 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 03:18:51,405 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 76 rows
2024-07-04 03:19:02,101 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 03:19:30,830 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 64 rows
2024-07-04 03:19:52,819 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 03:19:54,375 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 03:20:01,567 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 03:21:05,786 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 230 rows
2024-07-04 03:21:43,593 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 03:22:00,429 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 74 rows
2024-07-04 03:22:07,171 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 63 rows
2024-07-04 03:22:26,702 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 137 rows
2024-07-04 03:22:27,854 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 03:22:33,233 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 61 rows
2024-07-04 03:22:39,704 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 03:22:41,147 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 03:25:21,091 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 71 rows
2024-07-04 03:25:34,493 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 119 rows
2024-07-04 03:26:16,545 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 51 rows
2024-07-04 03:26:19,024 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 03:26:43,738 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 03:26:45,308 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 03:26:45,902 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 03:26:52,353 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 03:26:55,015 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 03:27:17,303 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 03:27:20,313 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 03:27:47,680 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 70 rows
2024-07-04 03:27:55,111 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 03:27:55,468 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 03:27:58,154 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 10 rows
2024-07-04 03:29:57,320 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 03:30:05,889 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 03:30:12,744 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 03:30:14,844 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 03:30:27,674 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 114 rows
2024-07-04 03:30:34,116 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 03:30:42,933 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 03:30:49,892 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 03:31:30,715 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 62 rows
2024-07-04 03:31:51,291 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-04 03:32:21,675 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 257 rows
2024-07-04 03:32:26,644 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-04 03:32:46,295 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 55 rows
2024-07-04 03:32:50,674 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 25 rows
2024-07-04 03:32:58,405 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 74 rows
2024-07-04 03:33:09,519 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 03:33:30,590 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 03:33:32,421 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 03:33:50,532 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 03:33:52,436 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 03:33:56,247 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 03:34:12,789 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 25 rows
2024-07-04 03:34:13,880 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 03:34:16,885 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 03:34:28,267 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 115 rows
2024-07-04 03:34:32,740 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 03:35:36,452 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 03:35:44,968 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 63 rows
2024-07-04 03:36:33,338 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 03:36:49,368 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 107 rows
2024-07-04 03:37:12,049 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 103 rows
2024-07-04 03:37:24,481 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 93 rows
2024-07-04 03:38:07,581 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 125 rows
2024-07-04 03:38:29,047 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 52 rows
2024-07-04 03:38:30,651 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 03:38:36,695 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 03:38:45,263 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 03:38:46,367 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-04 03:38:47,696 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-04 03:39:25,489 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 03:39:36,027 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 68 rows
2024-07-04 03:39:37,943 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 03:39:49,641 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 106 rows
2024-07-04 03:40:10,978 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 03:41:22,333 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-04 03:41:37,660 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 57 rows
2024-07-04 03:41:59,380 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 107 rows
2024-07-04 03:43:38,089 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 770 rows
2024-07-04 03:43:39,365 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 03:44:09,372 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 66 rows
2024-07-04 03:47:05,013 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 189 rows
2024-07-04 03:47:09,265 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 03:47:25,023 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 03:47:30,564 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 03:47:39,312 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 77 rows
2024-07-04 03:48:09,451 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 263 rows
2024-07-04 03:48:21,143 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 118 rows
2024-07-04 03:48:28,751 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 80 rows
2024-07-04 03:48:49,645 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 64 rows
2024-07-04 03:49:12,205 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 03:49:25,597 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 03:49:28,957 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 03:49:33,935 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 03:49:36,051 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-04 03:49:39,369 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 03:51:13,456 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 351 rows
2024-07-04 03:51:14,192 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 03:51:21,249 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 03:51:25,413 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 03:51:31,731 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 74 rows
2024-07-04 03:51:48,604 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 133 rows
2024-07-04 03:51:51,019 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 03:51:56,733 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 53 rows
2024-07-04 03:52:35,246 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 132 rows
2024-07-04 03:52:38,811 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-04 03:53:47,479 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 03:53:56,105 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 61 rows
2024-07-04 03:54:11,120 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 131 rows
2024-07-04 03:54:51,139 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 03:55:34,763 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 289 rows
2024-07-04 03:57:15,274 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 103 rows
2024-07-04 03:57:21,399 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 03:57:35,777 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 126 rows
2024-07-04 03:57:45,541 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 61 rows
2024-07-04 03:57:45,948 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 03:57:50,674 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 03:57:51,482 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 03:57:59,236 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 84 rows
2024-07-04 03:58:00,375 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 03:59:26,813 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 513 rows
2024-07-04 03:59:27,342 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 03:59:45,736 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 113 rows
2024-07-04 04:01:04,691 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 04:01:15,979 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 04:01:20,110 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 04:01:26,967 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 04:01:43,373 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 73 rows
2024-07-04 04:01:46,247 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 04:01:48,421 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 04:01:53,126 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 04:02:01,442 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 04:02:05,783 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 04:02:44,137 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 62 rows
2024-07-04 04:02:50,005 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 40 rows
2024-07-04 04:03:27,407 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 210 rows
2024-07-04 04:03:28,845 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 04:04:01,060 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 51 rows
2024-07-04 04:04:42,338 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 74 rows
2024-07-04 04:04:49,304 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 53 rows
2024-07-04 04:04:53,114 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-04 04:04:54,317 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 04:05:00,068 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 04:05:25,886 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 280 rows
2024-07-04 04:05:41,598 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 91 rows
2024-07-04 04:06:45,144 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 86 rows
2024-07-04 04:06:47,333 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 04:06:55,397 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 04:06:57,519 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-04 04:07:29,702 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-04 04:07:33,164 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 04:07:33,582 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 04:07:43,405 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 105 rows
2024-07-04 04:07:58,994 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 04:08:23,057 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 152 rows
2024-07-04 04:08:33,033 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 04:08:36,487 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 04:09:04,440 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 65 rows
2024-07-04 04:09:04,831 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 04:09:14,898 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 04:09:15,217 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 04:10:38,323 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 73 rows
2024-07-04 04:10:39,722 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 04:10:40,160 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 04:12:47,857 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 04:13:10,624 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 120 rows
2024-07-04 04:13:13,433 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 04:13:16,221 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 04:13:25,952 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 69 rows
2024-07-04 04:13:29,690 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 04:13:30,889 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 10 rows
2024-07-04 04:13:39,506 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-04 04:13:59,142 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 123 rows
2024-07-04 04:15:21,846 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-04 04:15:33,144 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 62 rows
2024-07-04 04:18:56,333 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1206 rows
2024-07-04 04:20:31,849 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 223 rows
2024-07-04 04:20:40,777 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 04:20:42,386 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 04:21:03,166 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 04:21:07,368 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 04:21:10,898 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 54 rows
2024-07-04 04:22:14,993 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 417 rows
2024-07-04 04:22:25,609 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 103 rows
2024-07-04 04:22:33,016 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 04:23:26,114 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 04:23:31,942 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 04:23:48,512 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 106 rows
2024-07-04 04:23:55,857 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-04 04:24:00,387 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-04 04:24:34,671 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 84 rows
2024-07-04 04:24:52,783 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 122 rows
2024-07-04 04:24:59,841 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 52 rows
2024-07-04 04:25:04,659 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 04:25:26,937 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 140 rows
2024-07-04 04:25:31,334 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 04:25:39,798 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 04:25:43,574 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 04:26:37,786 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 53 rows
2024-07-04 04:26:58,245 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 61 rows
2024-07-04 04:29:05,557 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 56 rows
2024-07-04 04:29:08,003 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 04:32:08,169 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1909 rows
2024-07-04 04:32:11,183 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 04:32:29,410 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 116 rows
2024-07-04 04:32:39,847 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 04:33:09,466 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 123 rows
2024-07-04 04:33:15,318 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 04:33:16,397 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 04:33:34,631 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 131 rows
2024-07-04 04:34:09,382 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 57 rows
2024-07-04 04:34:15,007 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 04:35:00,424 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 115 rows
2024-07-04 04:35:02,553 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 04:35:02,966 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 04:35:06,694 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 04:35:33,660 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-04 04:36:09,150 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 121 rows
2024-07-04 04:36:20,802 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 74 rows
2024-07-04 04:36:24,745 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 04:36:40,171 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 91 rows
2024-07-04 04:36:52,551 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 56 rows
2024-07-04 04:37:08,081 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 140 rows
2024-07-04 04:37:18,181 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 04:37:21,922 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 04:37:27,559 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 04:37:27,902 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 04:37:44,388 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 04:38:06,630 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 79 rows
2024-07-04 04:38:47,914 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 85 rows
2024-07-04 04:38:48,398 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 04:40:18,461 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 04:40:20,557 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 04:40:52,470 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-04 04:41:11,883 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 81 rows
2024-07-04 04:41:20,210 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 55 rows
2024-07-04 04:44:16,326 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1732 rows
2024-07-04 04:44:22,965 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 85 rows
2024-07-04 04:44:30,785 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 04:46:01,442 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1015 rows
2024-07-04 04:47:48,332 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 723 rows
2024-07-04 04:47:50,191 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-04 04:47:51,213 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 04:48:09,954 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 04:48:12,015 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-04 04:48:12,433 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 04:48:13,510 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 04:49:16,547 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 04:50:45,788 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 04:50:53,338 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 61 rows
2024-07-04 04:51:32,429 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 72 rows
2024-07-04 04:51:57,444 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 80 rows
2024-07-04 04:52:30,143 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 62 rows
2024-07-04 04:52:41,964 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 103 rows
2024-07-04 04:52:57,499 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 124 rows
2024-07-04 04:53:04,021 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 04:53:11,403 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 69 rows
2024-07-04 04:55:15,696 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1088 rows
2024-07-04 04:55:16,291 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 04:55:21,701 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 04:55:35,717 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-04 04:55:36,460 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 04:56:01,465 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 237 rows
2024-07-04 04:56:26,087 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 04:56:49,846 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 04:56:55,342 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 04:57:11,285 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 80 rows
2024-07-04 04:57:46,796 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 04:58:03,858 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 128 rows
2024-07-04 04:58:12,624 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 113 rows
2024-07-04 04:59:06,939 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 88 rows
2024-07-04 04:59:07,285 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 04:59:11,694 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 04:59:16,141 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 04:59:28,129 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 05:00:10,110 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 05:00:21,994 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 133 rows
2024-07-04 05:00:23,480 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 05:00:28,929 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 50 rows
2024-07-04 05:00:31,496 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 05:00:45,423 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 05:01:33,124 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 05:01:48,089 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-04 05:02:09,304 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 74 rows
2024-07-04 05:02:51,588 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 05:03:04,637 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-04 05:03:07,095 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 05:03:29,259 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 05:03:36,720 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 118 rows
2024-07-04 05:03:40,050 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 05:03:59,093 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 05:04:03,925 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 05:04:17,950 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 83 rows
2024-07-04 05:05:04,859 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 05:05:43,125 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 240 rows
2024-07-04 05:05:48,499 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 51 rows
2024-07-04 05:05:54,965 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 77 rows
2024-07-04 05:05:56,583 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 05:06:01,373 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 05:06:06,907 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 05:08:04,077 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 05:08:10,721 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 92 rows
2024-07-04 05:08:14,189 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 49 rows
2024-07-04 05:08:51,822 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 66 rows
2024-07-04 05:08:53,094 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 05:08:55,414 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 05:08:55,708 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 05:10:23,655 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 261 rows
2024-07-04 05:10:24,120 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 05:10:31,718 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 05:10:42,496 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 131 rows
2024-07-04 05:10:53,200 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 05:10:53,527 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 05:10:59,627 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 54 rows
2024-07-04 05:11:00,269 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 05:11:09,147 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 05:11:14,413 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 70 rows
2024-07-04 05:11:16,334 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 05:12:07,063 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 170 rows
2024-07-04 05:12:22,057 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 05:12:26,276 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 05:12:36,495 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 118 rows
2024-07-04 05:12:54,451 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 75 rows
2024-07-04 05:13:25,957 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 05:13:39,555 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 78 rows
2024-07-04 05:13:50,124 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 05:13:53,901 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 05:14:16,385 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 159 rows
2024-07-04 05:14:26,367 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 05:14:29,896 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-04 05:14:36,289 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 05:15:00,885 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 79 rows
2024-07-04 05:15:04,616 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-04 05:15:05,534 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 05:15:51,343 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 135 rows
2024-07-04 05:16:42,593 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 203 rows
2024-07-04 05:16:46,136 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 05:16:47,888 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 10 rows
2024-07-04 05:16:48,243 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 05:16:54,749 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 14 rows
2024-07-04 05:16:57,760 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 05:16:59,481 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 14 rows
2024-07-04 05:17:07,388 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-04 05:17:11,043 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 05:17:18,496 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 75 rows
2024-07-04 05:17:51,211 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 155 rows
2024-07-04 05:17:58,125 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-04 05:18:10,285 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-04 05:18:10,604 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 05:18:33,688 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 05:18:42,967 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 05:18:53,986 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 54 rows
2024-07-04 05:19:04,345 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 05:19:08,365 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 05:19:13,172 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 05:19:13,512 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 05:19:26,097 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 124 rows
2024-07-04 05:19:33,764 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 05:19:34,668 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 05:19:38,498 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 05:19:52,686 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 249 rows
2024-07-04 05:19:54,214 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 05:21:12,138 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 220 rows
2024-07-04 05:21:22,239 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-04 05:21:35,378 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 124 rows
2024-07-04 05:21:45,606 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 102 rows
2024-07-04 05:21:46,809 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-04 05:22:56,893 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 05:22:57,241 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 05:23:23,199 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 05:24:06,083 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 122 rows
2024-07-04 05:24:31,254 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 52 rows
2024-07-04 05:24:31,634 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 05:25:27,829 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 649 rows
2024-07-04 05:25:28,847 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 05:25:35,912 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 05:25:41,973 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 05:25:42,288 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 05:25:46,851 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 05:25:58,598 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 05:28:36,108 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 53 rows
2024-07-04 05:28:41,279 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 53 rows
2024-07-04 05:28:43,630 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 05:28:46,936 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 05:28:47,769 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 05:28:51,559 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 05:28:59,119 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 05:29:21,818 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 61 rows
2024-07-04 05:29:32,607 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 05:29:34,351 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-04 05:29:36,134 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 05:29:40,921 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 56 rows
2024-07-04 05:29:45,441 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-04 05:29:47,018 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 05:29:49,707 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 10 rows
2024-07-04 05:30:34,738 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 05:31:23,065 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 05:32:21,724 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-04 05:32:26,443 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 05:32:28,844 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 25 rows
2024-07-04 05:34:14,221 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 158 rows
2024-07-04 05:35:02,328 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 05:35:21,887 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 05:35:31,238 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 71 rows
2024-07-04 05:35:39,084 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 05:35:42,488 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 05:35:43,636 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 05:35:54,675 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 05:36:11,441 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 110 rows
2024-07-04 05:36:12,425 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 05:36:29,807 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 114 rows
2024-07-04 05:36:31,755 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 05:36:44,215 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 142 rows
2024-07-04 05:36:46,719 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-04 05:37:13,209 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 80 rows
2024-07-04 05:38:26,867 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 522 rows
2024-07-04 05:38:31,611 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 05:38:50,367 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 69 rows
2024-07-04 05:39:06,296 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 161 rows
2024-07-04 05:39:59,339 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 43 rows
2024-07-04 05:40:00,502 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 10 rows
2024-07-04 05:40:04,069 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 05:40:14,032 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 89 rows
2024-07-04 05:40:43,661 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 73 rows
2024-07-04 05:41:23,298 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 123 rows
2024-07-04 05:42:16,123 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 25 rows
2024-07-04 05:42:24,806 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-04 05:42:26,341 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 05:42:38,183 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 05:42:40,547 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-04 05:42:51,184 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 05:43:35,315 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 67 rows
2024-07-04 05:43:36,311 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 05:43:57,256 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 43 rows
2024-07-04 05:44:15,716 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 72 rows
2024-07-04 05:44:22,166 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 129 rows
2024-07-04 05:44:24,770 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 05:44:27,868 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 05:44:39,084 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 107 rows
2024-07-04 05:44:51,884 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 05:44:54,471 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 05:45:18,364 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 05:45:22,816 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 53 rows
2024-07-04 05:45:28,903 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 110 rows
2024-07-04 05:45:42,579 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 05:46:12,426 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 107 rows
2024-07-04 05:46:21,456 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 106 rows
2024-07-04 05:46:43,079 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 119 rows
2024-07-04 05:47:03,136 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 106 rows
2024-07-04 05:47:19,451 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 05:47:27,598 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-04 05:47:38,481 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 05:47:45,457 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 05:47:56,222 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-04 05:48:02,466 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 43 rows
2024-07-04 05:48:03,761 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 05:48:24,037 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 70 rows
2024-07-04 05:49:25,804 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 159 rows
2024-07-04 05:49:51,574 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 05:49:52,641 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 10 rows
2024-07-04 05:50:08,817 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 05:50:10,488 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 05:50:17,284 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-04 05:50:17,743 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 05:51:38,226 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 581 rows
2024-07-04 05:51:39,440 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 05:51:45,493 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 05:52:00,867 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 58 rows
2024-07-04 05:52:08,400 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 05:52:23,834 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 05:52:28,926 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 05:53:03,843 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 180 rows
2024-07-04 05:53:56,551 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 05:53:59,105 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 40 rows
2024-07-04 05:53:59,555 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 05:54:03,035 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 05:54:22,431 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 05:54:22,736 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 05:54:23,320 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 05:54:49,964 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 135 rows
2024-07-04 05:54:57,387 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 62 rows
2024-07-04 05:55:06,136 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 05:55:08,290 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 05:55:12,940 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 05:55:23,129 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 85 rows
2024-07-04 05:55:39,453 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 89 rows
2024-07-04 05:55:46,058 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 25 rows
2024-07-04 05:55:51,617 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 05:55:54,825 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 05:55:56,249 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 05:56:34,391 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 91 rows
2024-07-04 05:56:49,730 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 100 rows
2024-07-04 05:56:50,282 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 05:57:06,902 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-04 05:58:07,585 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 130 rows
2024-07-04 05:58:08,288 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 05:58:21,536 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 05:58:27,793 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 05:58:28,150 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 05:58:30,577 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 05:59:07,580 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 78 rows
2024-07-04 05:59:49,392 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 05:59:50,889 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 06:00:11,076 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 107 rows
2024-07-04 06:00:20,063 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 06:00:27,682 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 06:02:11,487 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1315 rows
2024-07-04 06:03:58,289 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1294 rows
2024-07-04 06:04:08,103 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 121 rows
2024-07-04 06:04:08,421 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 06:04:36,042 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 81 rows
2024-07-04 06:04:45,562 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 49 rows
2024-07-04 06:05:09,401 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 06:05:39,110 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 203 rows
2024-07-04 06:05:42,568 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-04 06:06:31,200 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 06:07:04,628 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 06:07:07,728 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 06:07:17,300 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 96 rows
2024-07-04 06:10:02,482 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 980 rows
2024-07-04 06:11:38,714 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 106 rows
2024-07-04 06:11:47,948 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 108 rows
2024-07-04 06:11:48,692 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 06:12:07,323 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 82 rows
2024-07-04 06:12:18,083 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 06:12:20,611 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 06:12:45,856 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 96 rows
2024-07-04 06:12:50,301 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 06:13:02,446 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 80 rows
2024-07-04 06:13:23,528 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 133 rows
2024-07-04 06:17:32,809 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 06:18:59,853 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1069 rows
2024-07-04 06:19:14,075 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 91 rows
2024-07-04 06:19:15,437 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 06:19:19,991 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-04 06:19:36,891 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 288 rows
2024-07-04 06:19:40,559 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 06:22:00,026 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 14 rows
2024-07-04 06:22:03,296 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 06:22:05,712 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 06:22:07,483 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 06:23:29,980 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 06:23:34,478 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 06:23:36,263 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 14 rows
2024-07-04 06:23:41,080 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 06:23:46,471 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 06:23:48,852 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 06:24:43,420 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 143 rows
2024-07-04 06:25:04,811 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 91 rows
2024-07-04 06:25:05,564 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 06:25:12,862 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 06:25:37,146 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-04 06:25:40,695 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 06:25:54,278 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 135 rows
2024-07-04 06:25:56,984 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 06:26:46,160 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-04 06:26:53,354 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 06:26:54,933 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 06:27:00,981 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 06:27:39,191 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 76 rows
2024-07-04 06:27:58,842 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 78 rows
2024-07-04 06:28:33,804 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 06:28:38,018 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 06:28:59,951 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 135 rows
2024-07-04 06:29:00,652 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 06:29:06,328 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 71 rows
2024-07-04 06:29:36,171 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 06:29:36,664 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 06:30:06,807 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 285 rows
2024-07-04 06:30:25,509 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 101 rows
2024-07-04 06:30:33,782 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 83 rows
2024-07-04 06:30:34,154 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 06:31:36,755 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 511 rows
2024-07-04 06:31:41,787 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 06:31:50,796 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 93 rows
2024-07-04 06:32:19,241 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 105 rows
2024-07-04 06:32:32,865 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 06:32:43,584 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 06:32:46,041 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 14 rows
2024-07-04 06:32:47,790 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 06:32:48,117 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 06:33:15,231 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 54 rows
2024-07-04 06:33:21,231 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 06:34:17,360 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 06:34:17,755 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 06:34:24,345 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 06:34:43,787 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 64 rows
2024-07-04 06:34:45,375 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 06:34:58,684 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 145 rows
2024-07-04 06:35:08,950 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 104 rows
2024-07-04 06:35:12,790 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 06:35:16,283 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 06:35:16,610 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 06:35:17,065 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 06:35:44,876 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 158 rows
2024-07-04 06:36:15,009 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 51 rows
2024-07-04 06:36:28,772 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 06:37:09,336 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 112 rows
2024-07-04 06:37:13,987 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 06:37:32,110 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 104 rows
2024-07-04 06:37:32,551 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 06:37:39,976 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 86 rows
2024-07-04 06:37:46,077 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 06:39:36,367 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 113 rows
2024-07-04 06:39:40,296 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 06:39:41,887 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 06:39:42,593 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 10 rows
2024-07-04 06:39:46,603 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 06:39:46,912 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 06:40:16,002 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-04 06:40:50,641 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 06:41:03,656 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 06:41:33,514 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 79 rows
2024-07-04 06:41:33,907 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 06:42:02,815 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 74 rows
2024-07-04 06:42:05,242 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 06:42:05,842 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 06:42:19,324 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 49 rows
2024-07-04 06:42:32,129 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 120 rows
2024-07-04 06:42:34,446 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 06:42:43,524 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 06:42:45,342 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 06:43:51,477 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 54 rows
2024-07-04 06:43:57,287 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 102 rows
2024-07-04 06:43:59,176 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-04 06:44:02,935 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 06:45:50,098 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 06:45:54,365 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 06:45:54,935 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 06:46:07,074 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 54 rows
2024-07-04 06:46:11,995 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 63 rows
2024-07-04 06:46:17,259 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 06:46:23,390 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 06:46:26,740 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 06:46:27,507 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 06:47:11,998 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 06:47:47,533 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 289 rows
2024-07-04 06:49:09,775 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 573 rows
2024-07-04 06:49:12,889 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 06:49:28,310 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 06:49:40,905 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 06:49:41,566 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 06:49:44,868 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 06:49:48,960 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 87 rows
2024-07-04 06:50:21,813 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 256 rows
2024-07-04 06:51:46,390 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1095 rows
2024-07-04 06:53:14,976 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 06:53:16,832 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 06:54:09,037 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 06:54:13,948 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-04 06:55:28,112 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 759 rows
2024-07-04 06:55:37,811 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 06:55:40,114 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-04 06:55:57,442 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 82 rows
2024-07-04 06:55:59,984 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 06:56:12,362 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 120 rows
2024-07-04 06:56:37,674 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 139 rows
2024-07-04 06:57:05,174 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 116 rows
2024-07-04 06:57:19,438 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 149 rows
2024-07-04 06:57:50,612 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 79 rows
2024-07-04 06:58:08,546 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 06:58:16,956 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 54 rows
2024-07-04 06:58:21,957 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 06:58:23,520 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 06:58:38,992 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 06:58:59,278 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 06:59:10,441 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 69 rows
2024-07-04 06:59:10,876 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 06:59:46,162 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 125 rows
2024-07-04 07:00:02,053 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 07:00:06,451 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-04 07:00:15,549 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 64 rows
2024-07-04 07:00:15,903 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 07:00:17,373 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 07:00:18,061 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 07:00:20,676 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 07:00:21,135 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 07:01:03,154 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 64 rows
2024-07-04 07:01:10,429 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 07:01:23,343 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 132 rows
2024-07-04 07:02:54,180 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 07:04:00,658 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 669 rows
2024-07-04 07:04:01,855 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 10 rows
2024-07-04 07:04:12,970 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 07:04:27,653 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 124 rows
2024-07-04 07:04:28,111 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 07:04:49,315 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 91 rows
2024-07-04 07:04:51,900 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 07:05:12,806 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 106 rows
2024-07-04 07:08:17,496 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1638 rows
2024-07-04 07:08:19,785 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 07:08:22,193 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 07:08:26,642 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 07:08:39,970 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 49 rows
2024-07-04 07:09:33,968 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 79 rows
2024-07-04 07:09:57,151 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 72 rows
2024-07-04 07:10:00,500 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 07:10:29,088 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 148 rows
2024-07-04 07:10:42,694 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 51 rows
2024-07-04 07:10:47,301 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 40 rows
2024-07-04 07:11:27,725 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 100 rows
2024-07-04 07:11:32,754 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 54 rows
2024-07-04 07:11:53,080 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 82 rows
2024-07-04 07:12:37,607 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 159 rows
2024-07-04 07:12:45,502 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 07:12:48,069 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 07:13:41,381 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 252 rows
2024-07-04 07:13:41,732 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 07:13:46,724 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 81 rows
2024-07-04 07:13:56,311 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 07:13:56,869 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 07:14:00,629 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-04 07:14:04,560 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 07:14:22,947 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 58 rows
2024-07-04 07:14:26,527 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 07:14:30,795 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 54 rows
2024-07-04 07:15:32,159 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 654 rows
2024-07-04 07:15:35,245 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 07:15:45,707 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 108 rows
2024-07-04 07:16:02,385 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 68 rows
2024-07-04 07:16:15,820 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 07:16:17,264 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 07:16:17,951 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 07:16:45,848 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 07:16:48,813 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 07:17:50,919 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 137 rows
2024-07-04 07:17:51,214 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 07:18:18,792 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 132 rows
2024-07-04 07:18:23,363 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 07:18:32,117 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 112 rows
2024-07-04 07:18:34,008 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 07:18:34,622 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 07:18:38,611 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 43 rows
2024-07-04 07:19:10,769 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 62 rows
2024-07-04 07:19:28,192 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 07:19:30,461 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 07:20:51,300 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 148 rows
2024-07-04 07:21:44,759 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 07:21:47,651 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 07:21:58,323 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 68 rows
2024-07-04 07:21:59,600 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 07:22:08,754 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 07:22:51,758 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 107 rows
2024-07-04 07:22:56,410 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 54 rows
2024-07-04 07:23:11,422 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 07:23:15,410 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 07:23:19,493 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 07:23:19,856 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 07:23:20,266 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 07:23:20,575 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 07:23:26,735 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 07:23:29,268 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 07:23:37,813 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 71 rows
2024-07-04 07:23:53,793 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 07:23:57,765 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 07:23:58,660 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 07:23:58,974 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 07:24:03,396 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 07:24:15,025 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 126 rows
2024-07-04 07:24:35,678 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 97 rows
2024-07-04 07:24:36,423 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 07:24:46,756 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 07:25:36,285 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-04 07:25:51,600 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 91 rows
2024-07-04 07:26:01,726 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 07:26:31,285 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 120 rows
2024-07-04 07:26:36,236 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 07:26:45,270 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 118 rows
2024-07-04 07:26:47,927 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 07:26:54,659 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 07:27:09,975 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 67 rows
2024-07-04 07:27:24,006 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 14 rows
2024-07-04 07:27:24,356 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 07:29:03,563 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 07:29:13,954 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 07:29:21,279 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 51 rows
2024-07-04 07:29:32,383 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 61 rows
2024-07-04 07:29:47,857 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 75 rows
2024-07-04 07:31:05,608 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 07:31:20,305 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 64 rows
2024-07-04 07:31:36,306 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 07:32:11,440 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 07:32:20,579 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 64 rows
2024-07-04 07:32:22,684 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-04 07:32:23,010 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 07:32:47,721 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 117 rows
2024-07-04 07:33:03,520 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 07:33:05,060 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 07:33:39,323 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 170 rows
2024-07-04 07:34:31,683 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 117 rows
2024-07-04 07:34:32,152 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 07:34:44,441 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 130 rows
2024-07-04 07:34:46,676 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-04 07:34:51,678 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-04 07:34:58,365 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-04 07:35:16,728 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 07:35:28,348 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 91 rows
2024-07-04 07:35:41,750 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 110 rows
2024-07-04 07:36:02,349 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-04 07:36:04,195 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 07:36:14,198 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 07:36:28,326 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 102 rows
2024-07-04 07:37:39,576 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 87 rows
2024-07-04 07:37:45,464 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 73 rows
2024-07-04 07:37:53,194 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 75 rows
2024-07-04 07:37:54,931 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 07:38:20,121 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 07:38:20,738 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 07:38:21,604 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 07:38:44,329 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 83 rows
2024-07-04 07:38:58,907 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 07:39:03,045 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 07:39:10,942 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 07:39:15,191 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 07:39:21,145 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 69 rows
2024-07-04 07:39:26,489 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 07:39:30,180 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 25 rows
2024-07-04 07:39:52,684 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 105 rows
2024-07-04 07:40:04,353 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 123 rows
2024-07-04 07:40:39,927 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 93 rows
2024-07-04 07:40:41,671 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 07:41:34,137 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 723 rows
2024-07-04 07:41:48,637 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 07:41:56,904 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 07:41:58,426 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 07:42:08,741 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 105 rows
2024-07-04 07:42:18,122 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 07:43:07,756 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 90 rows
2024-07-04 07:43:15,125 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 07:43:31,787 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 40 rows
2024-07-04 07:43:41,509 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 65 rows
2024-07-04 07:44:16,910 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 89 rows
2024-07-04 07:44:25,242 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 07:44:25,697 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 07:44:41,372 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 07:44:59,123 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-04 07:45:14,794 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 07:46:35,727 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 723 rows
2024-07-04 07:47:06,699 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 106 rows
2024-07-04 07:47:34,311 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 07:47:35,549 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 07:47:51,035 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 07:47:54,318 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 07:48:10,623 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-04 07:48:16,546 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 124 rows
2024-07-04 07:48:21,115 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 07:48:26,368 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 07:48:29,641 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 07:49:00,403 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 49 rows
2024-07-04 07:49:03,922 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 07:49:58,787 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 07:51:25,981 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1073 rows
2024-07-04 07:51:40,765 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 43 rows
2024-07-04 07:51:41,758 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 07:51:42,406 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 07:51:44,256 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 07:51:56,861 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 128 rows
2024-07-04 07:51:57,862 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 07:53:25,970 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 07:53:32,150 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 07:54:02,324 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 07:54:03,984 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 07:54:14,044 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 07:54:21,937 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 07:54:41,921 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 92 rows
2024-07-04 07:54:47,598 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 07:54:57,099 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 63 rows
2024-07-04 07:54:59,853 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 07:55:12,958 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 07:55:24,647 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 137 rows
2024-07-04 07:55:27,981 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 54 rows
2024-07-04 07:55:40,315 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 07:55:51,420 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 86 rows
2024-07-04 07:55:54,528 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 07:55:58,531 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 07:56:40,267 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 63 rows
2024-07-04 07:56:43,948 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 07:56:45,337 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 10 rows
2024-07-04 07:56:49,869 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-04 07:57:07,792 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 07:57:10,342 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-04 07:57:16,534 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-04 07:57:22,577 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 67 rows
2024-07-04 07:57:50,673 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 43 rows
2024-07-04 07:57:50,991 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 07:57:52,227 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 07:58:02,732 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 134 rows
2024-07-04 07:58:16,493 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 07:58:17,565 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 07:58:36,367 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 96 rows
2024-07-04 07:58:50,998 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 14 rows
2024-07-04 07:58:53,353 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 08:00:23,979 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1087 rows
2024-07-04 08:00:48,930 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 08:01:00,927 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 08:01:30,424 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 74 rows
2024-07-04 08:01:46,981 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 140 rows
2024-07-04 08:01:50,445 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 08:01:55,216 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 53 rows
2024-07-04 08:02:14,409 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 58 rows
2024-07-04 08:02:16,927 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-04 08:02:19,266 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 08:03:28,038 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 538 rows
2024-07-04 08:03:32,682 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 08:03:41,128 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 25 rows
2024-07-04 08:05:08,107 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1069 rows
2024-07-04 08:05:22,962 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 124 rows
2024-07-04 08:05:55,242 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 123 rows
2024-07-04 08:06:05,442 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 66 rows
2024-07-04 08:06:10,111 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 08:06:15,496 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 08:06:20,039 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 08:06:22,488 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-04 08:06:24,997 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 08:06:28,470 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 08:06:30,737 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 08:06:37,149 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 08:06:41,743 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 08:06:56,851 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 51 rows
2024-07-04 08:07:22,450 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 08:07:23,527 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 08:07:24,169 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 08:07:51,953 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 293 rows
2024-07-04 08:08:02,364 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 87 rows
2024-07-04 08:08:14,584 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 08:08:17,017 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 08:09:43,672 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 86 rows
2024-07-04 08:09:52,012 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 107 rows
2024-07-04 08:10:01,318 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 40 rows
2024-07-04 08:10:01,855 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 08:10:06,631 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 08:10:48,956 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 08:11:28,561 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 66 rows
2024-07-04 08:11:37,205 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 99 rows
2024-07-04 08:12:04,218 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 203 rows
2024-07-04 08:12:13,880 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-04 08:12:14,302 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 08:12:19,617 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 08:12:24,578 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-04 08:12:26,503 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 08:12:26,959 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 08:12:35,446 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 128 rows
2024-07-04 08:12:39,122 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 08:12:41,495 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 08:13:20,316 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 65 rows
2024-07-04 08:13:29,640 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 08:13:31,266 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 08:13:53,853 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 14 rows
2024-07-04 08:14:07,138 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 53 rows
2024-07-04 08:14:07,582 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 08:14:13,278 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 69 rows
2024-07-04 08:14:15,212 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 08:14:24,709 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 89 rows
2024-07-04 08:14:52,479 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 215 rows
2024-07-04 08:15:06,408 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 08:15:33,157 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 93 rows
2024-07-04 08:15:37,188 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 08:15:59,732 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 08:16:02,512 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 08:16:13,949 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 08:16:22,922 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 08:16:30,401 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 53 rows
2024-07-04 08:16:42,610 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 84 rows
2024-07-04 08:16:43,173 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 08:17:25,211 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 104 rows
2024-07-04 08:17:26,319 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 10 rows
2024-07-04 08:17:54,661 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 08:17:57,497 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 08:19:24,914 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1091 rows
2024-07-04 08:19:35,056 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 08:19:35,376 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 08:19:40,019 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 08:19:48,872 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-04 08:20:10,266 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 142 rows
2024-07-04 08:20:41,542 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 90 rows
2024-07-04 08:22:18,257 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 14 rows
2024-07-04 08:23:33,847 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 115 rows
2024-07-04 08:23:50,773 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 172 rows
2024-07-04 08:25:20,650 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 726 rows
2024-07-04 08:25:21,587 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 08:25:28,001 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 58 rows
2024-07-04 08:25:31,735 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 08:25:49,058 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 130 rows
2024-07-04 08:25:52,647 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 08:26:16,820 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 166 rows
2024-07-04 08:26:18,666 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-04 08:27:10,813 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 172 rows
2024-07-04 08:27:11,521 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 08:27:16,138 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 50 rows
2024-07-04 08:27:17,477 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-04 08:27:18,058 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 08:27:19,049 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 08:28:32,990 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 08:28:43,750 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 08:28:45,811 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 08:28:55,094 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-04 08:28:57,846 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 08:29:06,600 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 86 rows
2024-07-04 08:29:07,925 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 08:29:24,341 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-04 08:29:29,611 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 08:29:30,507 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 08:29:41,459 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 122 rows
2024-07-04 08:29:42,702 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 08:30:13,077 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 175 rows
2024-07-04 08:30:37,346 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 108 rows
2024-07-04 08:31:12,232 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 503 rows
2024-07-04 08:31:39,293 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 52 rows
2024-07-04 08:32:14,505 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 43 rows
2024-07-04 08:32:24,245 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 08:32:26,704 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 08:32:33,448 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-04 08:33:00,634 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 101 rows
2024-07-04 08:33:11,757 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 69 rows
2024-07-04 08:33:39,403 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-04 08:33:51,927 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 08:33:54,410 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 08:33:57,441 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 08:34:18,689 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 57 rows
2024-07-04 08:34:30,786 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 08:34:43,970 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 66 rows
2024-07-04 08:34:48,313 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 43 rows
2024-07-04 08:36:52,846 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-04 08:37:07,997 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 08:37:14,817 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 75 rows
2024-07-04 08:37:18,568 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 08:37:21,006 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 08:37:22,704 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 08:37:23,649 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 08:37:24,206 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 08:37:28,523 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 50 rows
2024-07-04 08:38:55,974 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1069 rows
2024-07-04 08:39:29,528 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 08:39:51,443 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 08:40:32,762 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 140 rows
2024-07-04 08:40:37,622 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 08:40:42,045 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 08:41:18,271 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 116 rows
2024-07-04 08:41:32,950 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 109 rows
2024-07-04 11:02:57,124 ｜ INFO ｜ milvus_helpers.py ｜ __init__ ｜ 14 ｜ Successfully connect to Milvus with IP:************* and PORT:19530
2024-07-04 11:04:01,650 ｜ INFO ｜ milvus_helpers.py ｜ __init__ ｜ 14 ｜ Successfully connect to Milvus with IP:************* and PORT:19530
2024-07-04 11:04:13,449 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 11:04:20,759 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 53 rows
2024-07-04 11:04:32,303 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 70 rows
2024-07-04 11:04:38,012 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 11:05:26,011 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 225 rows
2024-07-04 11:05:35,174 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 11:10:48,309 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 11:12:04,558 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 110 rows
2024-07-04 11:12:37,031 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 78 rows
2024-07-04 11:12:58,145 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-04 11:13:06,272 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 87 rows
2024-07-04 11:13:09,501 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 11:13:09,877 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 11:14:32,253 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 769 rows
2024-07-04 11:15:06,238 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 118 rows
2024-07-04 11:15:49,691 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 86 rows
2024-07-04 11:15:56,938 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 11:17:57,670 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 268 rows
2024-07-04 11:18:49,394 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 65 rows
2024-07-04 11:18:58,424 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 11:19:21,579 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-04 11:20:36,256 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 209 rows
2024-07-04 11:20:44,547 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 52 rows
2024-07-04 11:24:08,945 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 175 rows
2024-07-04 11:26:24,793 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 183 rows
2024-07-04 11:28:01,990 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 92 rows
2024-07-04 11:29:11,888 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-04 11:29:21,355 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 74 rows
2024-07-04 11:29:44,151 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 149 rows
2024-07-04 11:29:53,082 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 11:30:23,804 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 11:30:25,903 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 11:30:28,943 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 11:31:03,283 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-04 11:31:18,169 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 93 rows
2024-07-04 11:31:30,303 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 69 rows
2024-07-04 11:35:18,477 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 142 rows
2024-07-04 11:35:47,048 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 115 rows
2024-07-04 11:35:55,757 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 62 rows
2024-07-04 11:37:08,668 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 11:37:16,341 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 52 rows
2024-07-04 11:38:42,313 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 11:38:46,600 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 11:39:04,521 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 145 rows
2024-07-04 11:39:26,835 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 116 rows
2024-07-04 11:39:37,059 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 65 rows
2024-07-04 11:41:07,280 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 111 rows
2024-07-04 11:42:58,053 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 11:43:07,348 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 11:43:53,980 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 52 rows
2024-07-04 11:43:57,659 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 11:44:01,279 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 11:44:11,443 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-04 11:45:17,989 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 288 rows
2024-07-04 11:52:31,370 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 11:52:46,873 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 98 rows
2024-07-04 12:00:52,038 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 12:01:25,720 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 53 rows
2024-07-04 12:01:43,231 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 145 rows
2024-07-04 12:04:35,562 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 12:04:38,669 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 12:04:47,360 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 114 rows
2024-07-04 12:05:21,107 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 12:05:37,556 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 12:11:20,728 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 12:13:01,930 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 99 rows
2024-07-04 12:16:42,788 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 72 rows
2024-07-04 12:16:50,859 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 12:20:58,783 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 106 rows
2024-07-04 12:23:07,258 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 62 rows
2024-07-04 12:28:32,846 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 12:28:51,438 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 243 rows
2024-07-04 12:29:26,520 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 145 rows
2024-07-04 12:29:38,604 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 120 rows
2024-07-04 12:29:50,572 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 130 rows
2024-07-04 12:29:51,646 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 12:29:56,252 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 12:29:58,893 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 12:30:02,771 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 12:30:20,889 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 65 rows
2024-07-04 12:30:34,515 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 65 rows
2024-07-04 12:30:41,908 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 122 rows
2024-07-04 12:34:11,484 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 71 rows
2024-07-04 12:34:13,171 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 12:34:16,016 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 12:36:13,600 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 248 rows
2024-07-04 12:36:24,330 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 123 rows
2024-07-04 12:36:42,847 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 106 rows
2024-07-04 12:36:55,128 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 62 rows
2024-07-04 12:37:14,781 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 74 rows
2024-07-04 12:37:17,501 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 12:37:55,347 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 75 rows
2024-07-04 12:38:25,646 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 107 rows
2024-07-04 12:42:33,445 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 80 rows
2024-07-04 12:44:25,756 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 69 rows
2024-07-04 12:44:33,227 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 12:49:11,477 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 40 rows
2024-07-04 12:49:58,531 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 128 rows
2024-07-04 12:50:43,155 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 114 rows
2024-07-04 12:53:53,499 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 12:55:01,895 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 126 rows
2024-07-04 12:55:12,250 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 12:55:18,579 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 12:56:06,726 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 85 rows
2024-07-04 12:56:08,571 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 12:56:10,171 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 14 rows
2024-07-04 12:56:19,367 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 55 rows
2024-07-04 13:03:21,669 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 594 rows
2024-07-04 13:04:13,599 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 97 rows
2024-07-04 13:04:59,178 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 13:05:14,189 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 13:11:17,596 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 13:11:47,428 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 72 rows
2024-07-04 13:11:51,215 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 13:13:11,446 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 357 rows
2024-07-04 13:13:15,686 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 13:13:21,086 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 75 rows
2024-07-04 13:13:30,050 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 80 rows
2024-07-04 13:13:37,771 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 13:13:50,759 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 97 rows
2024-07-04 13:13:53,049 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 13:13:56,733 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 13:13:57,424 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 13:13:59,101 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 13:14:00,852 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 14 rows
2024-07-04 13:14:06,959 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 13:14:09,708 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 13:14:12,232 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 13:16:40,944 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 13:17:00,940 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 101 rows
2024-07-04 13:17:11,871 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 133 rows
2024-07-04 13:17:24,827 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 134 rows
2024-07-04 13:17:33,993 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 100 rows
2024-07-04 13:18:22,260 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-04 13:20:12,693 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 13:20:28,667 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 58 rows
2024-07-04 13:22:17,473 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 13:24:31,736 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 73 rows
2024-07-04 13:27:52,395 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 68 rows
2024-07-04 13:31:49,845 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 57 rows
2024-07-04 13:32:54,214 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 97 rows
2024-07-04 13:33:03,755 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 123 rows
2024-07-04 13:33:06,702 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 13:33:32,626 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 65 rows
2024-07-04 13:34:43,872 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 56 rows
2024-07-04 13:35:00,751 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-04 13:35:03,593 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 13:36:00,154 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 76 rows
2024-07-04 13:36:44,098 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 13:36:49,153 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 57 rows
2024-07-04 13:36:54,000 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 74 rows
2024-07-04 13:36:56,380 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 13:36:57,115 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 13:39:17,519 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 160 rows
2024-07-04 13:39:24,145 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 13:39:28,941 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 13:39:35,506 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 13:40:56,421 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 117 rows
2024-07-04 13:41:29,782 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 195 rows
2024-07-04 13:41:49,554 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 13:42:13,285 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 50 rows
2024-07-04 13:42:25,235 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 13:42:33,758 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-04 13:42:42,246 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 13:43:15,847 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 13:43:19,378 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 13:43:33,299 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 43 rows
2024-07-04 13:43:36,704 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 13:43:46,202 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 89 rows
2024-07-04 13:43:53,794 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 52 rows
2024-07-04 13:43:56,285 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 13:43:59,890 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 13:44:27,164 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 71 rows
2024-07-04 13:45:48,567 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-04 13:46:15,442 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 54 rows
2024-07-04 13:47:39,736 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 13:49:10,180 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 115 rows
2024-07-04 13:49:14,197 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 13:49:17,950 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 13:49:21,799 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 13:50:06,916 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 82 rows
2024-07-04 13:50:07,393 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 13:50:43,934 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 13:52:49,609 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 917 rows
2024-07-04 13:53:02,512 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 136 rows
2024-07-04 13:53:06,775 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 13:53:13,096 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 13:53:15,336 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 25 rows
2024-07-04 13:53:25,999 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-04 13:55:23,770 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 13:55:47,378 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 146 rows
2024-07-04 13:55:56,420 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 13:56:28,049 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 75 rows
2024-07-04 13:57:01,219 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 13:57:29,051 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 309 rows
2024-07-04 13:57:58,420 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 13:59:43,995 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1180 rows
2024-07-04 14:00:21,823 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 94 rows
2024-07-04 14:00:43,342 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 14:00:53,607 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 110 rows
2024-07-04 14:00:55,563 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 14:01:28,459 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 247 rows
2024-07-04 14:01:31,937 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 14:01:53,701 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 77 rows
2024-07-04 14:04:05,318 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1235 rows
2024-07-04 14:04:13,273 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 84 rows
2024-07-04 14:04:18,796 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 14:04:30,029 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 124 rows
2024-07-04 14:04:44,368 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 100 rows
2024-07-04 14:06:43,320 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 14:08:25,905 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 160 rows
2024-07-04 14:08:38,669 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 90 rows
2024-07-04 14:08:49,143 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 51 rows
2024-07-04 14:09:07,245 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 209 rows
2024-07-04 14:09:10,568 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 14:09:17,845 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-04 14:09:36,482 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 14:09:42,510 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 14:10:54,496 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 254 rows
2024-07-04 14:11:10,416 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 149 rows
2024-07-04 14:11:38,145 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 162 rows
2024-07-04 14:11:47,487 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 51 rows
2024-07-04 14:12:04,987 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-04 14:12:09,044 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 14:13:04,393 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 520 rows
2024-07-04 14:13:08,093 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 40 rows
2024-07-04 14:13:29,807 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 85 rows
2024-07-04 14:13:50,576 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 134 rows
2024-07-04 14:14:09,850 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 137 rows
2024-07-04 14:15:18,229 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 127 rows
2024-07-04 14:15:24,295 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 14:16:34,229 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 14:16:48,299 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 81 rows
2024-07-04 14:17:04,741 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 14:17:08,398 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 14:17:17,824 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 79 rows
2024-07-04 14:17:24,506 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 14:17:50,273 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 14:18:00,451 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 14:18:07,313 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 14:18:27,517 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 68 rows
2024-07-04 14:18:57,055 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-04 14:19:41,979 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 14:19:50,287 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 75 rows
2024-07-04 14:21:16,857 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 14:21:32,357 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 91 rows
2024-07-04 14:21:38,974 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 79 rows
2024-07-04 14:22:41,147 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 87 rows
2024-07-04 14:23:05,555 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 43 rows
2024-07-04 14:23:13,170 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 50 rows
2024-07-04 14:23:17,500 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-04 14:23:17,973 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 14:23:24,536 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 14:24:20,227 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 266 rows
2024-07-04 14:24:23,447 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 14:24:33,891 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 14:24:39,321 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 70 rows
2024-07-04 14:24:40,687 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 14:24:48,607 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 65 rows
2024-07-04 14:25:13,919 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 122 rows
2024-07-04 14:25:30,840 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 64 rows
2024-07-04 14:25:35,131 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 14:25:57,552 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 88 rows
2024-07-04 14:26:28,077 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 106 rows
2024-07-04 14:27:05,523 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 55 rows
2024-07-04 14:27:23,460 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-04 14:29:41,082 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 109 rows
2024-07-04 14:33:31,946 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 68 rows
2024-07-04 14:33:36,724 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 14:33:48,020 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 125 rows
2024-07-04 14:34:01,824 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 105 rows
2024-07-04 14:34:11,789 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 14:35:12,406 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 14:36:25,539 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 14:36:39,925 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 104 rows
2024-07-04 14:36:59,321 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 188 rows
2024-07-04 14:37:08,163 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 14:37:15,017 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 14:37:21,726 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 14:37:27,007 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 25 rows
2024-07-04 14:37:31,071 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 14:37:39,701 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 68 rows
2024-07-04 14:37:43,328 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 14:37:53,264 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 14:38:21,400 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 101 rows
2024-07-04 14:39:11,582 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 14:39:18,443 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 14:39:34,853 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 112 rows
2024-07-04 14:39:46,167 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 14:39:50,079 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 14:41:43,998 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 81 rows
2024-07-04 14:42:15,674 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 129 rows
2024-07-04 14:42:23,317 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 14:42:43,966 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 65 rows
2024-07-04 14:42:49,307 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 14:42:59,520 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 90 rows
2024-07-04 14:43:06,421 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 56 rows
2024-07-04 14:43:21,852 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 14:44:05,832 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 57 rows
2024-07-04 14:44:16,062 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 71 rows
2024-07-04 14:46:19,342 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 968 rows
2024-07-04 14:46:53,198 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-04 14:46:55,327 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 14:48:02,778 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 130 rows
2024-07-04 14:49:08,080 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 43 rows
2024-07-04 14:49:19,285 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 123 rows
2024-07-04 14:49:41,089 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 169 rows
2024-07-04 14:49:47,306 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 25 rows
2024-07-04 14:50:14,325 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 179 rows
2024-07-04 14:50:32,921 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 77 rows
2024-07-04 14:50:51,412 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 79 rows
2024-07-04 14:50:57,891 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 14:51:28,586 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 149 rows
2024-07-04 14:51:31,459 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 14:51:46,267 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 101 rows
2024-07-04 14:52:38,564 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 63 rows
2024-07-04 14:52:44,330 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 52 rows
2024-07-04 14:54:28,431 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 86 rows
2024-07-04 14:55:39,068 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 14:55:41,256 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 14:55:49,905 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 96 rows
2024-07-04 14:55:54,633 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 14:56:01,453 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 70 rows
2024-07-04 14:56:12,387 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 120 rows
2024-07-04 14:56:28,052 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 120 rows
2024-07-04 14:56:31,155 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 14:56:51,581 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 14:56:52,487 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 14:57:31,443 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 335 rows
2024-07-04 14:57:41,020 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 91 rows
2024-07-04 14:57:52,465 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 14:58:09,290 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 14:59:46,878 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 14:59:50,445 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 15:01:55,855 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 745 rows
2024-07-04 15:02:07,480 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 73 rows
2024-07-04 15:02:10,594 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 15:02:19,514 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 15:02:38,766 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 139 rows
2024-07-04 15:03:02,654 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 96 rows
2024-07-04 15:04:27,795 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 15:04:53,798 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 40 rows
2024-07-04 15:05:31,020 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 103 rows
2024-07-04 15:05:49,343 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 89 rows
2024-07-04 15:05:55,060 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 58 rows
2024-07-04 15:06:41,481 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 15:06:46,193 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 15:06:49,368 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 15:07:20,735 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 15:07:23,843 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 15:07:36,418 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 97 rows
2024-07-04 15:08:06,921 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 82 rows
2024-07-04 15:08:17,800 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 100 rows
2024-07-04 15:08:38,398 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 116 rows
2024-07-04 15:08:50,838 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 40 rows
2024-07-04 15:11:25,050 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 238 rows
2024-07-04 15:11:30,911 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 15:11:43,940 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 130 rows
2024-07-04 15:11:55,148 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 120 rows
2024-07-04 15:12:13,907 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 84 rows
2024-07-04 15:12:33,179 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 179 rows
2024-07-04 15:13:03,283 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 75 rows
2024-07-04 15:13:07,817 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 15:13:27,860 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 15:13:36,134 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 74 rows
2024-07-04 15:13:36,753 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 15:14:03,913 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 57 rows
2024-07-04 15:14:08,911 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 14 rows
2024-07-04 15:16:43,780 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 15:16:55,062 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 73 rows
2024-07-04 15:17:02,092 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-04 15:17:14,383 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 132 rows
2024-07-04 15:17:30,938 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 131 rows
2024-07-04 15:18:08,932 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 86 rows
2024-07-04 15:18:40,527 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-04 15:18:42,123 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 15:18:44,501 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 15:18:48,474 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 15:19:49,145 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 15:20:24,278 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 15:20:29,322 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 15:20:34,086 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 15:21:34,250 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 123 rows
2024-07-04 15:21:39,759 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 15:21:46,632 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-04 15:21:50,490 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 15:22:28,650 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 362 rows
2024-07-04 15:24:46,049 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1220 rows
2024-07-04 15:24:59,035 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 15:25:04,598 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 15:25:40,521 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 79 rows
2024-07-04 15:26:01,231 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 73 rows
2024-07-04 15:26:11,623 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 107 rows
2024-07-04 15:26:21,956 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 55 rows
2024-07-04 15:26:29,317 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 71 rows
2024-07-04 15:26:38,630 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 100 rows
2024-07-04 15:26:42,716 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 15:28:21,429 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 901 rows
2024-07-04 15:28:27,693 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 75 rows
2024-07-04 15:29:37,278 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 15:29:44,696 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 70 rows
2024-07-04 15:29:58,569 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 69 rows
2024-07-04 15:30:05,606 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 67 rows
2024-07-04 15:30:16,991 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 61 rows
2024-07-04 15:30:19,720 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 15:30:28,179 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 65 rows
2024-07-04 15:31:23,302 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 15:31:32,451 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 15:31:38,136 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 15:31:44,343 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-04 15:31:59,748 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 97 rows
2024-07-04 15:32:06,029 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 15:32:14,559 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 15:32:34,724 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 93 rows
2024-07-04 15:32:50,051 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 15:33:00,175 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 15:33:25,946 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 73 rows
2024-07-04 15:33:26,441 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 15:33:29,785 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 15:36:29,338 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 15:36:31,457 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-04 15:37:38,330 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 111 rows
2024-07-04 15:37:42,914 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-04 15:37:58,591 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 15:38:31,166 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 15:38:37,646 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 63 rows
2024-07-04 15:38:39,855 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 15:39:37,065 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 327 rows
2024-07-04 15:39:39,987 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 15:39:41,670 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 15:39:47,719 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 54 rows
2024-07-04 15:39:49,698 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 15:39:58,557 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 15:40:11,037 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 15:40:16,949 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 53 rows
2024-07-04 15:40:17,749 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 15:40:28,052 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 15:40:51,701 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 253 rows
2024-07-04 15:41:01,134 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 15:41:14,777 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 131 rows
2024-07-04 15:41:40,395 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 83 rows
2024-07-04 15:41:52,401 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 121 rows
2024-07-04 15:41:56,783 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 15:42:01,879 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 15:42:39,422 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 335 rows
2024-07-04 15:42:52,333 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-04 15:42:54,771 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 15:43:02,482 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 61 rows
2024-07-04 15:43:31,926 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 57 rows
2024-07-04 15:43:40,178 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 15:43:45,811 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 15:43:47,236 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 15:43:53,863 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 15:43:58,417 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 15:45:34,724 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1141 rows
2024-07-04 15:45:41,568 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 15:45:52,288 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 109 rows
2024-07-04 15:45:54,110 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 15:46:22,404 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 15:46:26,095 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 15:46:30,671 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 15:46:47,335 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 15:47:14,574 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 114 rows
2024-07-04 15:48:59,301 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-04 15:50:40,638 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 172 rows
2024-07-04 15:51:16,286 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 117 rows
2024-07-04 15:51:18,533 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 15:51:25,283 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-04 15:51:29,477 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 15:51:37,680 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 51 rows
2024-07-04 15:52:31,424 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 15:52:32,247 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 15:52:34,787 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-04 15:52:49,281 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 73 rows
2024-07-04 15:53:28,486 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 276 rows
2024-07-04 15:54:02,087 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 15:55:03,398 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 499 rows
2024-07-04 15:56:30,913 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 575 rows
2024-07-04 15:56:32,655 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 15:56:35,066 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-04 15:56:39,774 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 15:56:47,119 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 15:57:11,129 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 51 rows
2024-07-04 15:57:15,454 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 15:57:23,201 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 98 rows
2024-07-04 15:57:32,096 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 52 rows
2024-07-04 15:57:37,471 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 15:58:52,997 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 15:58:57,650 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 54 rows
2024-07-04 15:58:58,171 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 15:59:39,622 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 16:00:19,193 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 96 rows
2024-07-04 16:00:23,093 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-04 16:00:24,709 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 14 rows
2024-07-04 16:00:29,229 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 16:00:36,996 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 16:00:50,738 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 86 rows
2024-07-04 16:01:09,966 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 73 rows
2024-07-04 16:01:21,576 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-04 16:01:57,559 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 215 rows
2024-07-04 16:02:25,767 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 185 rows
2024-07-04 16:02:35,188 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 120 rows
2024-07-04 16:02:43,144 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 16:03:13,155 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 149 rows
2024-07-04 16:03:16,407 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 16:04:31,338 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 16:04:41,641 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 86 rows
2024-07-04 16:04:46,803 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 16:04:59,464 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 131 rows
2024-07-04 16:05:04,923 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 58 rows
2024-07-04 16:05:28,013 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 52 rows
2024-07-04 16:05:34,312 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 16:05:35,535 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 16:05:46,949 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 92 rows
2024-07-04 16:05:49,122 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 16:06:01,616 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 95 rows
2024-07-04 16:06:05,178 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-04 16:06:16,516 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 10 rows
2024-07-04 16:06:20,998 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 16:06:21,465 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 16:06:35,502 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 94 rows
2024-07-04 16:06:38,497 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-04 16:06:41,706 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 16:07:54,679 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 388 rows
2024-07-04 16:08:00,706 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 16:08:03,947 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 16:08:13,396 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 16:08:28,336 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 66 rows
2024-07-04 16:08:50,206 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 64 rows
2024-07-04 16:08:53,553 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 16:09:07,750 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 125 rows
2024-07-04 16:09:50,998 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 16:09:53,355 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 16:10:10,620 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 208 rows
2024-07-04 16:10:11,694 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 16:10:15,922 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 16:10:20,584 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 16:10:25,828 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 16:10:36,099 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 43 rows
2024-07-04 16:10:41,267 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 25 rows
2024-07-04 16:10:47,259 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 50 rows
2024-07-04 16:11:00,386 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 119 rows
2024-07-04 16:11:08,583 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 77 rows
2024-07-04 16:11:20,976 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 73 rows
2024-07-04 16:11:21,308 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 16:11:22,306 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 16:11:24,995 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 16:11:26,575 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 16:11:29,158 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 16:11:50,994 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 151 rows
2024-07-04 16:12:02,398 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 62 rows
2024-07-04 16:12:05,389 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 16:12:14,729 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 16:12:42,841 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 16:12:52,403 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 93 rows
2024-07-04 16:13:10,959 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 16:13:17,391 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 16:13:19,861 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 16:13:21,290 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 16:13:25,670 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 16:13:29,756 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 16:14:02,753 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 274 rows
2024-07-04 16:15:06,460 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 723 rows
2024-07-04 16:15:10,361 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 40 rows
2024-07-04 16:15:17,258 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 43 rows
2024-07-04 16:15:19,241 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 16:15:24,922 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 16:15:33,072 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 64 rows
2024-07-04 16:16:08,910 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 16:16:12,419 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 16:16:20,953 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 16:16:29,192 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 63 rows
2024-07-04 16:16:36,166 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-04 16:16:53,111 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 95 rows
2024-07-04 16:17:01,621 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 16:17:09,140 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 65 rows
2024-07-04 16:17:14,102 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 51 rows
2024-07-04 16:17:15,831 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 10 rows
2024-07-04 16:17:24,796 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 16:17:27,719 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 16:17:31,072 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 16:17:48,536 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 84 rows
2024-07-04 16:17:58,781 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 49 rows
2024-07-04 16:17:59,702 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 16:18:32,213 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 16:18:51,260 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 105 rows
2024-07-04 16:18:52,239 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 16:18:57,497 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 40 rows
2024-07-04 16:19:23,535 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 170 rows
2024-07-04 16:19:31,187 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 16:19:37,648 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 52 rows
2024-07-04 16:19:47,268 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 75 rows
2024-07-04 16:20:55,510 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 76 rows
2024-07-04 16:20:55,913 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 16:21:19,471 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-04 16:21:21,279 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 16:21:25,845 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 16:21:50,305 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 207 rows
2024-07-04 16:22:14,525 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 90 rows
2024-07-04 16:22:29,345 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 88 rows
2024-07-04 16:22:34,382 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 16:22:52,032 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 64 rows
2024-07-04 16:22:57,742 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 16:23:11,858 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 16:23:39,301 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 115 rows
2024-07-04 16:23:48,032 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 16:24:40,396 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 94 rows
2024-07-04 16:24:50,441 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-04 16:24:52,881 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-04 16:24:58,148 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 16:25:00,436 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 14 rows
2024-07-04 16:25:07,609 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 80 rows
2024-07-04 16:25:23,901 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 16:25:32,002 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 73 rows
2024-07-04 16:25:34,008 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-04 16:26:19,888 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 68 rows
2024-07-04 16:26:24,379 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 16:28:36,093 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 74 rows
2024-07-04 16:28:39,394 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 16:29:03,327 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 87 rows
2024-07-04 16:29:21,872 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 169 rows
2024-07-04 16:29:41,629 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 74 rows
2024-07-04 16:29:47,911 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 16:29:53,229 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 16:30:12,300 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 16:30:17,533 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 43 rows
2024-07-04 16:30:21,767 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 16:30:29,533 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 16:33:05,650 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 143 rows
2024-07-04 16:35:04,838 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1211 rows
2024-07-04 16:36:41,565 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1099 rows
2024-07-04 16:36:46,461 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 54 rows
2024-07-04 16:36:51,418 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 51 rows
2024-07-04 16:36:51,748 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 16:37:00,324 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 84 rows
2024-07-04 16:38:04,095 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 16:38:11,745 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 16:38:14,078 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 16:38:22,945 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 16:38:59,757 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 168 rows
2024-07-04 16:39:07,688 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 105 rows
2024-07-04 16:39:15,061 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 80 rows
2024-07-04 16:39:27,128 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 16:39:31,171 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 16:41:31,210 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1214 rows
2024-07-04 16:42:16,450 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 153 rows
2024-07-04 16:42:22,377 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 71 rows
2024-07-04 16:43:31,713 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 16:43:35,747 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 16:43:42,761 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 16:43:48,476 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 61 rows
2024-07-04 16:43:51,339 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 16:44:08,322 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 49 rows
2024-07-04 16:44:14,940 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 55 rows
2024-07-04 16:46:05,323 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 16:46:07,963 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 16:46:10,471 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 16:46:13,943 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 16:46:18,413 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 16:46:23,722 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 16:46:38,112 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 126 rows
2024-07-04 16:47:02,994 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 16:48:01,568 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 51 rows
2024-07-04 16:49:10,061 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 16:49:14,116 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 16:49:37,230 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 180 rows
2024-07-04 16:50:06,741 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 154 rows
2024-07-04 16:50:10,051 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 14 rows
2024-07-04 16:50:21,116 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 97 rows
2024-07-04 16:50:46,918 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-04 16:50:47,549 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 16:50:51,857 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 16:51:01,579 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 107 rows
2024-07-04 16:51:10,918 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 99 rows
2024-07-04 16:51:43,846 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 94 rows
2024-07-04 16:51:44,689 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 16:51:59,245 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 80 rows
2024-07-04 16:52:01,170 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 16:52:28,756 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 99 rows
2024-07-04 16:52:33,347 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 16:52:41,903 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 85 rows
2024-07-04 16:52:48,208 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 16:52:50,534 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 16:52:56,175 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-04 16:53:38,568 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 165 rows
2024-07-04 16:53:42,184 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 16:53:43,472 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 16:53:50,988 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 66 rows
2024-07-04 16:53:58,802 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 67 rows
2024-07-04 16:54:04,408 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-04 16:54:10,962 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 16:54:13,385 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 16:54:26,409 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 82 rows
2024-07-04 16:54:29,299 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 16:54:37,212 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 16:55:04,746 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 16:55:12,107 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-04 16:55:20,916 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 53 rows
2024-07-04 16:55:25,830 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 16:55:31,775 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 43 rows
2024-07-04 16:55:44,472 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 112 rows
2024-07-04 16:55:53,473 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 49 rows
2024-07-04 16:55:57,795 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 53 rows
2024-07-04 16:56:04,876 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 16:56:54,905 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 645 rows
2024-07-04 16:58:34,454 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1093 rows
2024-07-04 16:58:36,099 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 16:58:45,293 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 91 rows
2024-07-04 16:58:47,739 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 17:00:43,892 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 17:01:05,595 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 101 rows
2024-07-04 17:03:45,600 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 954 rows
2024-07-04 17:04:08,165 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 117 rows
2024-07-04 17:04:47,112 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 75 rows
2024-07-04 17:05:06,600 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 17:06:07,390 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 723 rows
2024-07-04 17:06:31,205 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 109 rows
2024-07-04 17:06:40,640 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-04 17:06:49,100 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 58 rows
2024-07-04 17:07:02,672 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 17:08:37,235 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1095 rows
2024-07-04 17:08:39,328 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 17:08:48,091 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-04 17:09:33,633 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 17:09:49,266 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 116 rows
2024-07-04 17:09:57,911 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-04 17:10:06,902 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 17:10:09,361 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 17:10:11,104 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 17:10:15,187 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 17:10:33,603 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 68 rows
2024-07-04 17:10:38,217 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 17:12:04,704 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 134 rows
2024-07-04 17:12:24,745 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 17:13:01,261 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 87 rows
2024-07-04 17:13:22,617 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 166 rows
2024-07-04 17:13:26,488 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 17:13:31,367 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 68 rows
2024-07-04 17:13:34,205 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 17:13:47,410 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 124 rows
2024-07-04 17:14:05,994 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 58 rows
2024-07-04 17:14:12,900 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 17:14:19,826 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 73 rows
2024-07-04 17:14:28,847 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 90 rows
2024-07-04 17:14:50,432 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 153 rows
2024-07-04 17:15:01,683 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 70 rows
2024-07-04 17:15:02,799 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 17:15:32,105 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 168 rows
2024-07-04 17:15:44,257 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 58 rows
2024-07-04 17:15:55,474 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 134 rows
2024-07-04 17:15:57,797 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 17:16:00,436 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 17:16:22,312 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 157 rows
2024-07-04 17:16:24,702 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-04 17:16:26,335 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 17:16:32,907 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 17:16:42,924 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 17:16:50,134 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-04 17:17:10,482 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 53 rows
2024-07-04 17:17:28,126 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-04 17:17:47,611 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 17:17:56,669 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 17:18:18,952 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 95 rows
2024-07-04 17:18:31,582 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 17:19:18,895 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 83 rows
2024-07-04 17:19:28,355 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 82 rows
2024-07-04 17:19:38,281 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 106 rows
2024-07-04 17:19:44,124 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 17:19:48,481 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 17:19:49,631 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 17:19:50,261 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 17:20:00,575 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 62 rows
2024-07-04 17:20:07,883 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 17:20:44,823 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 111 rows
2024-07-04 17:21:03,655 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 17:21:16,793 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 77 rows
2024-07-04 17:21:21,467 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 14 rows
2024-07-04 17:21:48,509 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 17:21:50,925 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 17:21:54,518 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 17:21:57,997 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 17:22:15,168 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 17:22:25,672 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 61 rows
2024-07-04 17:22:26,995 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 17:23:40,753 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 75 rows
2024-07-04 17:24:58,573 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 79 rows
2024-07-04 17:25:45,533 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 160 rows
2024-07-04 17:25:52,003 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 25 rows
2024-07-04 17:26:09,622 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 64 rows
2024-07-04 17:26:19,101 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 17:26:23,026 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 43 rows
2024-07-04 17:26:53,771 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 172 rows
2024-07-04 17:26:59,318 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 53 rows
2024-07-04 17:27:07,570 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 58 rows
2024-07-04 17:27:12,197 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 17:27:16,105 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 17:27:32,444 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 112 rows
2024-07-04 17:27:47,530 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 17:27:59,543 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 17:28:02,832 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 17:28:46,367 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 65 rows
2024-07-04 17:28:50,496 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 17:28:55,373 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 17:29:17,263 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 152 rows
2024-07-04 17:29:23,146 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 17:29:34,825 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 106 rows
2024-07-04 17:29:54,554 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 17:29:56,041 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 14 rows
2024-07-04 17:30:00,726 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 17:30:16,664 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 74 rows
2024-07-04 17:30:39,290 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 68 rows
2024-07-04 17:30:42,321 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 17:30:45,116 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 17:31:05,790 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 53 rows
2024-07-04 17:31:12,869 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 65 rows
2024-07-04 17:31:53,478 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-04 17:32:03,193 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 95 rows
2024-07-04 17:32:25,167 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 110 rows
2024-07-04 17:32:52,883 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 169 rows
2024-07-04 17:33:31,060 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 17:33:39,594 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 17:33:58,234 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 106 rows
2024-07-04 17:34:34,340 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 186 rows
2024-07-04 17:34:35,763 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 17:34:40,920 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 40 rows
2024-07-04 17:34:42,565 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 17:34:58,284 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-04 17:35:00,333 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 17:35:53,823 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 17:36:03,116 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 74 rows
2024-07-04 17:36:27,966 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 162 rows
2024-07-04 17:36:32,611 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 17:36:43,244 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 51 rows
2024-07-04 17:37:44,414 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 779 rows
2024-07-04 17:38:12,779 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 154 rows
2024-07-04 17:38:17,411 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 54 rows
2024-07-04 17:39:18,557 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 649 rows
2024-07-04 17:39:22,842 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 17:39:24,781 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-04 17:40:44,408 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 701 rows
2024-07-04 17:41:04,416 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 53 rows
2024-07-04 17:41:10,652 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 53 rows
2024-07-04 17:41:15,017 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 17:41:15,571 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 17:41:24,532 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 54 rows
2024-07-04 17:41:45,937 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 66 rows
2024-07-04 17:41:48,534 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 17:41:55,241 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 17:42:15,094 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 17:42:55,361 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 17:43:13,510 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 51 rows
2024-07-04 17:43:27,681 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 66 rows
2024-07-04 17:43:36,519 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 57 rows
2024-07-04 17:43:58,520 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 235 rows
2024-07-04 17:45:20,721 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 17:46:48,138 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 17:46:52,113 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 53 rows
2024-07-04 17:47:00,919 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 17:47:03,142 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 17:47:08,522 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 17:47:28,215 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 17:48:01,323 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 123 rows
2024-07-04 17:48:05,234 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 17:48:07,299 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 14 rows
2024-07-04 17:49:36,199 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 17:49:38,255 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 10 rows
2024-07-04 17:50:09,933 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 134 rows
2024-07-04 17:50:11,894 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 17:50:38,705 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 17:53:16,476 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 17:53:18,545 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-04 17:53:45,282 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 235 rows
2024-07-04 17:53:48,968 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 17:53:53,585 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 55 rows
2024-07-04 17:54:26,381 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 87 rows
2024-07-04 17:54:28,103 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 17:56:15,991 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 17:56:23,504 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 71 rows
2024-07-04 17:56:39,323 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 102 rows
2024-07-04 17:56:45,986 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 17:56:54,220 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 88 rows
2024-07-04 17:56:58,131 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 17:57:02,494 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 17:57:08,149 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 73 rows
2024-07-04 17:57:09,639 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 17:57:12,357 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 17:57:16,423 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 17:57:55,800 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 77 rows
2024-07-04 17:58:10,780 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 122 rows
2024-07-04 17:58:25,109 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 17:58:25,957 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 17:58:32,951 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 17:58:35,354 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 17:58:44,664 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 69 rows
2024-07-04 17:58:54,406 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 17:59:11,152 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 178 rows
2024-07-04 17:59:28,392 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 97 rows
2024-07-04 18:00:05,958 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 18:00:10,017 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 18:00:15,871 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 18:00:24,892 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 52 rows
2024-07-04 18:00:26,415 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 18:00:49,750 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 18:00:50,942 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 18:00:51,964 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 18:01:36,345 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 18:01:42,465 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 51 rows
2024-07-04 18:01:55,611 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 55 rows
2024-07-04 18:02:07,304 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 118 rows
2024-07-04 18:02:09,595 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 18:02:31,437 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 68 rows
2024-07-04 18:04:13,423 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1147 rows
2024-07-04 18:04:21,305 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 18:04:23,807 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 18:05:27,076 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 18:05:49,913 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 152 rows
2024-07-04 18:06:02,558 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 119 rows
2024-07-04 18:08:46,833 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 165 rows
2024-07-04 18:09:25,087 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-04 18:09:27,652 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 18:10:01,011 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 97 rows
2024-07-04 18:10:08,057 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 85 rows
2024-07-04 18:10:20,256 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 103 rows
2024-07-04 18:10:21,411 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 18:10:25,669 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 40 rows
2024-07-04 18:10:37,646 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 120 rows
2024-07-04 18:10:49,702 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 111 rows
2024-07-04 18:10:57,626 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 91 rows
2024-07-04 18:11:19,906 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 18:12:20,945 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 96 rows
2024-07-04 18:12:43,117 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 18:12:54,335 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 73 rows
2024-07-04 18:13:28,822 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 18:13:33,491 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-04 18:13:33,951 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 18:13:46,103 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 119 rows
2024-07-04 18:15:04,461 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 624 rows
2024-07-04 18:15:06,650 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 14 rows
2024-07-04 18:15:11,295 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 18:15:11,685 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 18:16:20,744 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 62 rows
2024-07-04 18:16:37,337 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 225 rows
2024-07-04 18:16:40,762 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 18:16:53,488 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 86 rows
2024-07-04 18:17:23,557 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 132 rows
2024-07-04 18:17:28,563 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 18:17:36,781 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 18:17:37,349 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 18:19:25,285 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 123 rows
2024-07-04 18:19:26,154 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 18:19:36,401 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 105 rows
2024-07-04 18:19:41,000 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 18:19:45,723 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 49 rows
2024-07-04 18:19:50,404 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-04 18:19:57,819 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 18:20:32,641 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 18:20:37,107 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 18:20:54,496 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 105 rows
2024-07-04 18:20:55,940 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 18:21:24,723 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 18:21:35,345 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 69 rows
2024-07-04 18:21:48,770 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 89 rows
2024-07-04 18:22:02,988 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 63 rows
2024-07-04 18:22:26,746 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 18:22:37,984 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 49 rows
2024-07-04 18:23:01,062 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 82 rows
2024-07-04 18:23:21,038 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 64 rows
2024-07-04 18:23:27,322 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 77 rows
2024-07-04 18:23:45,238 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 18:24:34,049 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 18:26:37,626 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 10 rows
2024-07-04 18:26:41,309 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 18:27:00,263 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 115 rows
2024-07-04 18:27:04,311 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 18:27:07,895 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 18:27:11,160 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 18:27:12,602 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 18:27:27,392 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 61 rows
2024-07-04 18:28:53,822 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 921 rows
2024-07-04 18:29:06,695 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 135 rows
2024-07-04 18:29:27,400 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 118 rows
2024-07-04 18:29:32,988 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 18:29:42,920 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 91 rows
2024-07-04 18:29:51,854 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 84 rows
2024-07-04 18:29:55,373 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 18:30:03,730 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-04 18:30:08,792 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 18:30:12,272 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 18:30:22,360 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 18:30:23,283 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 18:30:23,638 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 18:31:18,882 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 103 rows
2024-07-04 18:31:20,489 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-04 18:31:24,031 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 18:31:25,119 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 18:31:26,044 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 18:31:26,583 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 18:31:33,517 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-04 18:31:48,537 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 65 rows
2024-07-04 18:32:07,690 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 63 rows
2024-07-04 18:32:10,437 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 18:32:18,976 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 64 rows
2024-07-04 18:32:21,206 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 18:32:33,502 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 136 rows
2024-07-04 18:32:41,496 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 18:32:50,281 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 18:33:07,866 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 18:33:22,633 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-04 18:33:50,963 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 50 rows
2024-07-04 18:34:29,444 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 18:34:31,337 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 18:34:34,717 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 18:34:35,977 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 18:36:38,987 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 240 rows
2024-07-04 18:36:48,899 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 87 rows
2024-07-04 18:37:03,455 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 18:37:09,424 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 18:37:20,084 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 105 rows
2024-07-04 18:37:21,666 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 18:37:28,933 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 100 rows
2024-07-04 18:37:50,334 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 18:38:06,847 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 18:38:10,262 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 18:38:44,897 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 333 rows
2024-07-04 18:38:50,906 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 18:38:54,985 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 18:39:08,745 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 58 rows
2024-07-04 18:39:13,656 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 18:39:40,867 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 185 rows
2024-07-04 18:39:41,801 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 18:39:44,302 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 18:40:02,625 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 57 rows
2024-07-04 18:40:19,875 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 18:40:23,667 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 40 rows
2024-07-04 18:40:27,945 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 18:40:32,430 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 18:41:10,397 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 82 rows
2024-07-04 18:41:16,286 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 18:41:26,450 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 114 rows
2024-07-04 18:41:46,209 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 18:41:50,436 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 18:41:52,537 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 18:41:54,375 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 10 rows
2024-07-04 18:41:55,355 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 18:41:59,902 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 18:42:08,307 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 61 rows
2024-07-04 18:44:08,447 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1230 rows
2024-07-04 18:44:14,483 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 18:44:21,014 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 18:44:30,564 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 75 rows
2024-07-04 18:44:43,516 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 91 rows
2024-07-04 18:44:51,241 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 91 rows
2024-07-04 18:44:53,881 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 18:45:12,631 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 18:47:32,655 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 18:47:38,782 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 18:47:48,096 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 73 rows
2024-07-04 18:47:49,265 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 18:47:50,443 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 18:48:30,533 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 18:48:38,117 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 40 rows
2024-07-04 18:48:44,029 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-04 18:48:52,105 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 115 rows
2024-07-04 18:49:09,717 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 18:49:14,585 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 18:49:19,179 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-04 18:49:25,638 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 58 rows
2024-07-04 18:50:30,906 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-04 18:50:47,135 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-04 18:50:49,109 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 18:51:14,521 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 18:56:10,937 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 85 rows
2024-07-04 18:58:28,409 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 18:58:39,742 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 91 rows
2024-07-04 19:02:19,080 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 19:04:49,932 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 64 rows
2024-07-04 19:04:56,246 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 19:11:06,413 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 19:11:34,691 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 71 rows
2024-07-04 19:11:36,275 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 19:11:41,050 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 19:11:42,036 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-04 19:11:50,765 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 43 rows
2024-07-04 19:11:57,362 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-04 19:12:12,804 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 19:13:54,817 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 79 rows
2024-07-04 19:14:16,931 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 19:18:20,952 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1061 rows
2024-07-04 19:19:32,959 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 19:20:36,417 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 19:21:42,635 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 19:22:05,327 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 19:22:21,876 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-04 19:22:37,661 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 90 rows
2024-07-04 19:23:10,347 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 90 rows
2024-07-04 19:23:12,691 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 19:23:38,705 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 94 rows
2024-07-04 19:25:05,238 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-04 19:32:06,807 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 42 rows
2024-07-04 19:36:19,874 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 121 rows
2024-07-04 19:37:07,414 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 19:37:34,591 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 166 rows
2024-07-04 19:37:38,384 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 19:37:43,209 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 19:37:44,803 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 19:38:14,781 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 287 rows
2024-07-04 19:38:40,368 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 97 rows
2024-07-04 19:39:11,536 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 19:39:27,380 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 64 rows
2024-07-04 19:39:59,102 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-04 19:40:20,269 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 145 rows
2024-07-04 19:40:22,004 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 19:40:36,530 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 70 rows
2024-07-04 19:41:16,434 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 167 rows
2024-07-04 19:41:40,076 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 123 rows
2024-07-04 19:41:46,667 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 19:42:25,600 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-04 19:43:27,177 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-04 19:43:37,042 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 77 rows
2024-07-04 19:44:35,441 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 126 rows
2024-07-04 19:44:46,402 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 100 rows
2024-07-04 19:44:48,016 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-04 19:44:53,350 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 19:45:04,021 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 71 rows
2024-07-04 19:46:05,180 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-04 19:46:13,415 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-04 19:46:40,563 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 203 rows
2024-07-04 19:46:43,699 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 19:47:57,811 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 144 rows
2024-07-04 19:47:59,235 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 19:48:08,875 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-04 19:48:23,806 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 121 rows
2024-07-04 19:48:36,073 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 53 rows
2024-07-04 19:49:44,501 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 19:49:53,606 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 19:50:14,116 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 52 rows
2024-07-04 19:50:19,168 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 19:52:14,352 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-04 19:52:17,536 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-04 19:52:25,022 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 69 rows
2024-07-04 19:52:25,988 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-04 19:52:40,521 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 80 rows
2024-07-04 19:52:48,948 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 77 rows
2024-07-04 19:53:02,469 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 19:53:05,642 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-04 19:53:25,586 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 243 rows
2024-07-04 20:01:31,466 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1255 rows
2024-07-04 20:02:41,860 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 145 rows
2024-07-04 20:03:12,874 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-04 20:10:45,987 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 20:12:01,749 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 20:13:35,164 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 70 rows
2024-07-04 20:14:38,614 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 208 rows
2024-07-04 20:16:31,634 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 100 rows
2024-07-04 20:19:36,792 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-04 20:19:40,433 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 20:19:46,364 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 70 rows
2024-07-04 20:19:49,190 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 20:19:59,449 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 93 rows
2024-07-04 20:20:14,777 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 20:20:18,550 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 20:23:50,581 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 82 rows
2024-07-04 20:26:39,425 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 94 rows
2024-07-04 20:28:05,444 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-04 20:29:15,858 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 51 rows
2024-07-04 20:31:21,994 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-04 20:31:24,981 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 20:31:42,740 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 77 rows
2024-07-04 20:31:45,052 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-04 20:31:45,806 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 20:31:47,069 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 20:31:50,861 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 20:32:41,278 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-04 20:32:57,014 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 49 rows
2024-07-04 20:33:01,620 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-04 20:33:37,711 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 176 rows
2024-07-04 20:33:39,147 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 10 rows
2024-07-04 20:33:42,935 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 20:33:49,828 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-04 20:33:50,518 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-04 20:34:06,999 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 50 rows
2024-07-04 20:34:09,035 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 20:34:11,053 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-04 20:34:17,646 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 52 rows
2024-07-04 20:34:25,561 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 71 rows
2024-07-04 20:34:27,089 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 14 rows
2024-07-04 20:35:41,294 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 88 rows
2024-07-04 20:54:46,703 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-04 20:54:55,690 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 67 rows
2024-07-04 21:02:37,129 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 67 rows
2024-07-04 21:02:43,981 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 74 rows
2024-07-04 21:04:23,923 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1140 rows
2024-07-04 21:04:35,326 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-04 21:04:39,932 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 40 rows
2024-07-04 21:05:00,301 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 57 rows
2024-07-04 21:05:40,553 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 21:07:32,114 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 25 rows
2024-07-04 21:07:45,745 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-04 21:10:32,663 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-04 21:11:29,771 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 21:12:47,913 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 43 rows
2024-07-04 21:14:38,648 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 206 rows
2024-07-04 21:14:50,831 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-04 21:15:02,982 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-04 21:16:32,384 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1070 rows
2024-07-04 21:16:59,295 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 116 rows
2024-07-04 21:17:46,783 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 100 rows
2024-07-04 21:18:55,281 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 68 rows
2024-07-04 21:24:31,626 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 10 rows
2024-07-04 21:25:22,061 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 140 rows
2024-07-04 21:27:12,084 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 21:27:14,243 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-04 21:27:21,602 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-04 21:28:59,135 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-04 21:29:02,790 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-04 21:31:18,104 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 78 rows
2024-07-04 21:35:24,900 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 124 rows
2024-07-04 21:35:34,454 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 85 rows
2024-07-04 21:36:38,725 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 78 rows
2024-07-04 21:36:42,445 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-04 21:40:51,991 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 213 rows
2024-07-04 21:41:18,729 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-04 21:42:32,056 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-04 21:44:46,698 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-04 21:45:02,968 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 156 rows
2024-07-04 21:46:26,330 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 50 rows
2024-07-04 21:46:38,337 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 122 rows
2024-07-04 21:54:13,346 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-04 21:57:53,010 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 77 rows
2024-07-04 22:03:48,431 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-04 22:07:07,095 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-04 22:11:58,658 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 77 rows
2024-07-04 22:13:23,962 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-04 22:13:31,617 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 77 rows
2024-07-04 22:14:00,309 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 27 rows
2024-07-04 22:20:16,942 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 74 rows
2024-07-04 22:20:27,261 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 83 rows
2024-07-04 22:20:40,403 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 85 rows
2024-07-04 22:20:41,481 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-04 22:22:50,808 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-04 22:23:08,964 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-04 22:24:42,254 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
