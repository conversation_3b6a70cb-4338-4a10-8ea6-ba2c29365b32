#\u7CFB\u7EDF\u5176\u5B83\u666E\u901A\u7684\u914D\u7F6E\u5E76\u53D7\u73AF\u5883\u5F71\u54CD\u7684\u7EDF\u4E00\u914D\u7F6E\u5728\u6B64
#common.tokenMap[tokenVerifyUrl]=http://uac.zte.com.cn/uactoken/auth/token/verify.serv
#Token\u6548\u9A8C\u65F6\u9700\u8981\u7528\u5230\u7684\u7CFB\u7EDF\u7F16\u7801
common.tokenMap[sysCode]=100000201191
#UAC\u7684Token\u7F13\u5B58\u8FC7\u671F\u65F6\u95F4(\u5206\u949F)
common.tokenMap[expiredTime]=30
#\u670D\u52A1\u5668\u5141\u4F7F\u7684\u8BF7\u6C42\u57DF\u540D
common.corsMap[allowedOrigin]=*
#\u670D\u52A1\u5668\u652F\u6301\u7684\u6240\u6709\u5934\u4FE1\u606F\u5B57\u6BB5\uFF0C\u4E0D\u9650\u4E8E\u6D4F\u89C8\u5668\u5728"\u9884\u68C0"\u4E2D\u8BF7\u6C42\u7684\u5B57\u6BB5
common.corsMap[allowedHeader]=*
#\u670D\u52A1\u5668\u652F\u6301\u7684\u6240\u6709\u8DE8\u57DF\u8BF7\u6C42\u7684\u65B9\u6CD5
common.corsMap[allowedMethod]=*
#\u672C\u6B21\u9884\u68C0\u8BF7\u6C42\u7684\u6709\u6548\u671F\uFF0C\u5355\u4F4D\u4E3A\u79D2,\u6709\u6548\u671F\u5185\u4E0D\u7528\u53D1\u51FA\u53E6\u4E00\u6761\u9884\u68C0\u8BF7\u6C42
common.corsMap[maxAge]=7200
#\u670D\u52A1\u5668\u652F\u6301\u9644\u5E26\u8EAB\u4EFD\u51ED\u8BC1\u7684\u8BF7\u6C42
common.corsMap[allowCredentials]=true
#\u4FEE\u5996\u6D88\u8D39\u7684topic\u4EE5\u53CA\u5BF9\u5E94\u7684partition
common.topics[0]=${spring.application.type}