2024-06-15 16:30:25,427 ｜ INFO ｜ milvus_helpers.py ｜ __init__ ｜ 14 ｜ Successfully connect to Milvus with IP:************* and PORT:19530
2024-06-15 16:30:25,568 ｜ INFO ｜ milvus_helpers.py ｜ create_collection ｜ 52 ｜ Create Milvus collection: productQA_cos_all_ivfflat
2024-06-15 16:30:26,243 ｜ INFO ｜ milvus_helpers.py ｜ create_index ｜ 107 ｜ Successfully create index in collection:productQA_cos_all_ivfflat with param:{'index_type': 'IVF_FLAT', 'metric_type': 'COSINE', 'params': {'nlist': 16384}}
2024-06-15 17:47:28,053 ｜ INFO ｜ milvus_helpers.py ｜ __init__ ｜ 14 ｜ Successfully connect to Milvus with IP:************* and PORT:19530
2024-06-15 17:47:30,367 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 11 rows
2024-06-15 17:47:31,076 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-15 17:47:35,482 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 17:47:36,867 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-15 17:47:38,057 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-15 17:47:39,085 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-15 17:47:40,154 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-15 17:47:41,078 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 17:47:41,749 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-15 17:47:46,367 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-15 17:47:47,414 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 14 rows
2024-06-15 17:47:53,749 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-15 17:47:54,716 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 17:47:55,252 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-15 17:47:56,370 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 17:47:56,783 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 17:47:57,231 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-15 17:47:59,023 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 10 rows
2024-06-15 17:48:00,783 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-15 17:48:02,839 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-15 17:48:04,092 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 17:48:04,511 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 17:48:06,382 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-15 17:48:07,409 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-15 17:48:07,887 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 17:48:12,000 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 17:48:13,085 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-15 17:48:14,710 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-15 17:48:15,793 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 11 rows
2024-06-15 18:05:58,040 ｜ INFO ｜ milvus_helpers.py ｜ __init__ ｜ 14 ｜ Successfully connect to Milvus with IP:************* and PORT:19530
2024-06-15 18:05:59,381 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-15 18:06:02,367 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:06:08,731 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-15 18:06:09,170 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:06:10,824 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-15 18:06:21,636 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-15 18:06:22,204 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-15 18:06:23,562 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:06:29,623 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 18:06:32,126 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 18:06:34,723 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 18:08:38,086 ｜ INFO ｜ milvus_helpers.py ｜ __init__ ｜ 14 ｜ Successfully connect to Milvus with IP:************* and PORT:19530
2024-06-15 18:09:55,704 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-15 18:10:33,207 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 18:10:52,244 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-15 18:12:47,547 ｜ INFO ｜ milvus_helpers.py ｜ __init__ ｜ 14 ｜ Successfully connect to Milvus with IP:************* and PORT:19530
2024-06-15 18:12:50,020 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 18:12:51,579 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 11 rows
2024-06-15 18:12:52,578 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:12:54,529 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:12:56,003 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 10 rows
2024-06-15 18:12:56,813 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 18:13:00,272 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 14 rows
2024-06-15 18:13:01,195 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:13:02,436 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-15 18:13:06,715 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 12 rows
2024-06-15 18:13:07,844 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-15 18:13:08,836 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:13:10,524 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 12 rows
2024-06-15 18:13:12,606 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:13:13,557 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 18:13:14,631 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:13:16,068 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 10 rows
2024-06-15 18:13:17,979 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 13 rows
2024-06-15 18:13:20,235 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:13:22,524 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 10 rows
2024-06-15 18:13:25,450 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-15 18:13:26,647 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-15 18:13:28,805 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-15 18:13:32,239 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 25 rows
2024-06-15 18:13:34,882 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 14 rows
2024-06-15 18:13:36,375 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-15 18:13:37,815 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-15 18:13:39,424 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 10 rows
2024-06-15 18:13:41,036 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 11 rows
2024-06-15 18:13:42,063 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:13:43,055 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:13:43,875 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 18:13:45,361 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 10 rows
2024-06-15 18:13:47,098 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-15 18:13:47,994 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:13:48,928 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 18:13:51,689 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 16 rows
2024-06-15 18:13:52,785 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-15 18:13:54,012 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-15 18:13:56,824 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-15 18:13:58,834 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 13 rows
2024-06-15 18:14:03,869 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 12 rows
2024-06-15 18:14:05,503 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 12 rows
2024-06-15 18:14:07,340 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-15 18:14:11,911 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 11 rows
2024-06-15 18:14:13,351 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-15 18:14:14,981 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 11 rows
2024-06-15 18:14:16,343 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-15 18:14:17,624 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-15 18:14:19,653 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-15 18:14:21,259 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 11 rows
2024-06-15 18:14:22,234 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:14:24,245 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 14 rows
2024-06-15 18:14:27,117 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 11 rows
2024-06-15 18:14:28,766 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 12 rows
2024-06-15 18:14:29,986 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-15 18:14:32,673 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-15 18:14:34,422 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:14:36,002 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 11 rows
2024-06-15 18:14:36,820 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 18:14:37,791 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 18:14:38,899 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-15 18:14:40,199 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-15 18:14:42,040 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 18:14:45,358 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 13 rows
2024-06-15 18:14:46,246 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:14:47,750 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 10 rows
2024-06-15 18:14:50,183 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-15 18:14:51,650 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 10 rows
2024-06-15 18:14:53,258 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 11 rows
2024-06-15 18:14:54,807 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 18:14:56,156 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-15 18:14:57,985 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-15 18:14:58,899 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:15:00,827 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 10 rows
2024-06-15 18:15:01,960 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-15 18:15:04,712 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 18 rows
2024-06-15 18:15:06,047 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-15 18:15:07,684 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 18:15:08,728 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-15 18:15:09,528 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 18:15:10,611 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-15 18:15:12,161 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:15:13,129 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:15:14,457 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-15 18:15:15,239 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 18:15:16,087 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 18:15:17,998 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 18:15:20,674 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 12 rows
2024-06-15 18:15:23,141 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:15:24,103 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:15:25,029 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:15:26,502 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 10 rows
2024-06-15 18:15:29,089 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 14 rows
2024-06-15 18:15:31,254 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 10 rows
2024-06-15 18:15:32,338 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-15 18:15:34,032 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:15:36,166 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-15 18:15:37,905 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:15:38,821 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:15:39,835 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:15:40,832 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 18:15:41,796 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:15:45,602 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 10 rows
2024-06-15 18:15:46,646 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-15 18:15:48,019 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-15 18:15:49,206 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-15 18:15:50,687 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 10 rows
2024-06-15 18:15:52,148 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 10 rows
2024-06-15 18:15:54,152 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 10 rows
2024-06-15 18:15:56,116 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 13 rows
2024-06-15 18:15:57,712 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-15 18:15:58,579 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:16:00,169 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:16:02,027 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:16:03,010 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:16:05,085 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 14 rows
2024-06-15 18:16:08,655 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 18:16:10,784 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 15 rows
2024-06-15 18:16:11,745 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:16:12,740 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:16:16,084 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 25 rows
2024-06-15 18:16:17,508 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 10 rows
2024-06-15 18:16:18,318 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 18:16:19,772 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-15 18:16:22,428 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-15 18:16:24,763 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 15 rows
2024-06-15 18:16:26,516 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 12 rows
2024-06-15 18:16:27,458 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:16:28,383 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:16:29,807 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 10 rows
2024-06-15 18:16:31,020 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-15 18:16:32,380 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:16:33,269 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:16:34,240 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:16:35,206 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:16:36,193 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:16:37,910 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 18:16:38,891 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:16:40,597 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 7 rows
2024-06-15 18:16:41,511 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:16:42,647 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-15 18:16:44,840 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 14 rows
2024-06-15 18:16:45,598 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 18:16:46,874 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 9 rows
2024-06-15 18:16:48,152 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-15 18:16:49,822 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 12 rows
2024-06-15 18:16:52,002 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:17:49,456 ｜ INFO ｜ milvus_helpers.py ｜ __init__ ｜ 14 ｜ Successfully connect to Milvus with IP:************* and PORT:19530
2024-06-15 18:17:50,598 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 6 rows
2024-06-15 18:17:58,528 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 8 rows
2024-06-15 18:18:00,870 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_cos_all_ivfflat with 5 rows
2024-06-15 18:19:09,507 ｜ INFO ｜ milvus_helpers.py ｜ __init__ ｜ 14 ｜ Successfully connect to Milvus with IP:************* and PORT:19530
2024-06-15 18:20:56,216 ｜ INFO ｜ milvus_helpers.py ｜ __init__ ｜ 14 ｜ Successfully connect to Milvus with IP:************* and PORT:19530
2024-06-15 18:25:47,293 ｜ INFO ｜ milvus_helpers.py ｜ __init__ ｜ 14 ｜ Successfully connect to Milvus with IP:************* and PORT:19530
2024-06-15 18:30:53,031 ｜ INFO ｜ milvus_helpers.py ｜ __init__ ｜ 14 ｜ Successfully connect to Milvus with IP:************* and PORT:19530
