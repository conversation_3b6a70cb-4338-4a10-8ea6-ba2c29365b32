# 文本Chunk导入向量库工具

## 功能说明
本工具用于将解析后的PDF文本chunk批量导入到Milvus向量库中，支持自动向量化和批量插入。

## 文件说明
- `chunk_import_tool.py` - 主要导入脚本
- `run_chunk_import.bat` - Windows批处理运行脚本
- `chunk_import_exceptions.txt` - 错误日志文件（自动生成）

## 使用步骤

### 方法1：直接运行批处理文件
1. 双击运行 `run_chunk_import.bat`
2. 等待导入完成

### 方法2：命令行运行
```bash
cd milvus
python chunk_import_tool.py
```

## 配置说明

### 目标文件夹
脚本默认处理以下文件夹：
```
D:\JAVA\workspace\AI\ZTE_AI\zte-ibo-acm-productretrieve_0804\text_chunks\parsed_example_pdf
```

如需修改，请编辑 `chunk_import_tool.py` 第112行的 `chunk_folder` 变量。

### Milvus集合名称
默认集合名称：`parsed_pdf_chunks`
如需修改，请编辑脚本第13行的 `collection_name` 变量。

## 处理流程
1. **连接Milvus** - 自动创建集合（如不存在）
2. **读取metadata** - 获取源文件信息
3. **批量处理chunk文件** - 按顺序处理所有chunk_*.txt文件
4. **向量化** - 调用ZTE内部API生成1024维向量
5. **批量插入** - 每10个文件批量插入一次
6. **统计报告** - 显示成功/失败统计

## 输出信息
- 实时处理进度
- 向量生成状态
- 批量插入结果
- 最终统计报告
- 失败文件列表

## 错误处理
- 自动跳过无法处理的文件
- 记录失败文件到异常日志
- 支持部分成功导入
- 显示详细错误信息

## 性能特点
- 批量处理提高效率
- 自动重试机制
- 内存优化（分批处理）
- 超时保护

## 注意事项
1. 确保Milvus服务正常运行
2. 确保网络可访问向量化API
3. 处理大量文件时请预留足够时间
4. 建议在网络稳定环境下运行

## 故障排除
- 如果向量化API调用失败，请检查网络连接
- 如果Milvus连接失败，请检查服务状态和配置
- 查看 `chunk_import_exceptions.txt` 了解详细错误信息
