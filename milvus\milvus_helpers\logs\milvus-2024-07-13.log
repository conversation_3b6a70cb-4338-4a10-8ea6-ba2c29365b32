2024-07-13 00:00:34,600 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 964 rows
2024-07-13 00:00:42,350 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-13 00:01:03,268 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 78 rows
2024-07-13 00:02:50,980 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 626 rows
2024-07-13 00:04:45,886 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 449 rows
2024-07-13 00:07:13,874 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 884 rows
2024-07-13 00:07:23,638 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-13 00:08:01,707 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 146 rows
2024-07-13 00:08:21,735 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 93 rows
2024-07-13 00:11:06,615 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 952 rows
2024-07-13 00:11:29,469 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 62 rows
2024-07-13 00:11:56,126 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 122 rows
2024-07-13 00:12:12,157 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 84 rows
2024-07-13 00:17:26,473 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1132 rows
2024-07-13 00:22:15,447 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1703 rows
2024-07-13 00:22:29,724 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-13 00:26:20,684 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 581 rows
2024-07-13 00:29:54,752 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 580 rows
2024-07-13 00:31:33,013 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 613 rows
2024-07-13 00:31:46,000 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-13 00:33:46,485 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 453 rows
2024-07-13 00:38:01,411 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1018 rows
2024-07-13 00:39:04,098 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 259 rows
2024-07-13 00:39:27,842 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-13 00:44:18,120 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1059 rows
2024-07-13 00:44:58,357 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 149 rows
2024-07-13 00:53:00,616 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 678 rows
2024-07-13 00:53:07,206 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-13 00:54:11,130 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 299 rows
2024-07-13 00:54:32,821 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-13 00:54:53,289 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 76 rows
2024-07-13 00:58:52,545 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1720 rows
2024-07-13 00:58:55,686 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-13 00:59:43,548 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 199 rows
2024-07-13 01:01:23,085 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 610 rows
2024-07-13 01:01:32,458 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-13 01:01:54,432 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-13 01:03:04,898 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 192 rows
2024-07-13 01:03:27,532 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-13 01:04:41,912 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 359 rows
2024-07-13 01:05:22,615 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 142 rows
2024-07-13 01:06:24,195 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 156 rows
2024-07-13 01:06:33,775 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-13 01:06:41,262 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 29 rows
2024-07-13 01:11:15,845 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 816 rows
2024-07-13 01:11:40,705 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 117 rows
2024-07-13 01:14:34,891 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 636 rows
2024-07-13 01:14:52,517 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-13 01:15:12,201 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 75 rows
2024-07-13 01:15:32,256 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-13 01:18:38,399 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1099 rows
2024-07-13 01:23:33,695 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1029 rows
2024-07-13 01:26:19,464 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 708 rows
2024-07-13 01:26:30,324 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-13 01:26:37,905 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-13 01:26:57,597 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-13 01:27:06,565 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-13 01:28:57,704 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 447 rows
2024-07-13 01:29:36,175 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 131 rows
2024-07-13 01:32:24,865 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 532 rows
2024-07-13 01:32:30,647 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-13 01:32:53,286 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 83 rows
2024-07-13 01:33:13,959 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-13 01:35:36,686 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 884 rows
2024-07-13 01:36:33,155 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 267 rows
2024-07-13 01:37:38,642 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 163 rows
2024-07-13 01:42:08,909 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 969 rows
2024-07-13 01:42:13,109 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-13 01:42:27,853 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 71 rows
2024-07-13 01:43:15,077 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 149 rows
2024-07-13 01:43:42,570 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 107 rows
2024-07-13 01:43:50,494 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 10 rows
2024-07-13 01:45:21,246 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 288 rows
2024-07-13 01:49:47,250 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 988 rows
2024-07-13 01:50:51,607 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 272 rows
2024-07-13 01:51:10,643 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 78 rows
2024-07-13 01:51:34,230 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 19 rows
2024-07-13 01:51:43,577 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-13 01:52:00,312 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 79 rows
2024-07-13 01:52:26,864 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 69 rows
2024-07-13 01:53:05,304 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 103 rows
2024-07-13 01:54:05,550 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 164 rows
2024-07-13 01:54:50,553 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 223 rows
2024-07-13 01:54:59,677 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-13 02:00:39,594 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1198 rows
2024-07-13 02:00:45,667 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-13 02:01:32,790 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 181 rows
2024-07-13 02:01:35,734 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2 rows
2024-07-13 02:03:29,712 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 358 rows
2024-07-13 02:03:44,845 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-13 02:05:09,498 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 176 rows
2024-07-13 02:05:22,867 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-13 02:06:02,961 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 231 rows
2024-07-13 02:06:41,069 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 195 rows
2024-07-13 02:06:55,670 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-13 02:06:58,681 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-13 02:07:42,882 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 160 rows
2024-07-13 02:08:17,277 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-13 02:14:37,842 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1813 rows
2024-07-13 02:15:17,941 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 195 rows
2024-07-13 02:17:53,971 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1110 rows
2024-07-13 02:19:13,471 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 258 rows
2024-07-13 02:19:18,911 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-13 02:19:44,707 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 69 rows
2024-07-13 02:20:07,739 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-13 02:20:24,359 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-13 02:21:08,341 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 229 rows
2024-07-13 02:25:40,471 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 915 rows
2024-07-13 02:25:50,744 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-13 02:25:58,369 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-13 02:26:20,102 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 70 rows
2024-07-13 02:26:54,963 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 182 rows
2024-07-13 02:29:39,812 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 946 rows
2024-07-13 02:30:07,853 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 79 rows
2024-07-13 02:30:15,807 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 32 rows
2024-07-13 02:31:07,044 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 261 rows
2024-07-13 02:31:57,442 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 224 rows
2024-07-13 02:40:24,146 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 720 rows
2024-07-13 02:44:50,685 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 936 rows
2024-07-13 02:45:07,789 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 66 rows
2024-07-13 02:45:27,823 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 75 rows
2024-07-13 02:45:40,464 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-13 02:49:16,944 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 530 rows
2024-07-13 02:49:37,765 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 71 rows
2024-07-13 02:52:59,509 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 298 rows
2024-07-13 02:53:27,826 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 80 rows
2024-07-13 02:53:37,095 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 56 rows
2024-07-13 02:53:45,835 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 41 rows
2024-07-13 02:54:11,464 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 96 rows
2024-07-13 02:54:34,208 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 72 rows
2024-07-13 02:54:48,770 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 66 rows
2024-07-13 02:55:20,793 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 77 rows
2024-07-13 02:55:36,638 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-13 02:56:01,530 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 126 rows
2024-07-13 02:56:17,550 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-13 02:56:37,812 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 74 rows
2024-07-13 02:58:03,355 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 561 rows
2024-07-13 02:58:13,658 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-13 02:58:33,794 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 79 rows
2024-07-13 02:58:54,857 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 81 rows
2024-07-13 03:08:30,100 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2741 rows
2024-07-13 03:11:13,684 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1071 rows
2024-07-13 03:13:07,761 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 518 rows
2024-07-13 03:16:35,598 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 925 rows
2024-07-13 03:16:50,050 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 52 rows
2024-07-13 03:17:17,592 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 116 rows
2024-07-13 03:21:12,759 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1667 rows
2024-07-13 03:21:19,902 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-13 03:21:28,289 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-13 03:26:11,711 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 868 rows
2024-07-13 03:29:08,126 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 586 rows
2024-07-13 03:30:07,553 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 270 rows
2024-07-13 03:30:45,207 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 244 rows
2024-07-13 03:31:15,469 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 120 rows
2024-07-13 03:31:53,989 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 175 rows
2024-07-13 03:32:20,278 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 122 rows
2024-07-13 03:33:50,629 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 299 rows
2024-07-13 03:33:58,038 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-13 03:34:03,698 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 10 rows
2024-07-13 03:34:13,782 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-13 03:35:16,155 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 178 rows
2024-07-13 03:35:32,079 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-13 03:38:24,300 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1251 rows
2024-07-13 03:38:31,119 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-13 03:41:35,458 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1226 rows
2024-07-13 03:41:50,839 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 45 rows
2024-07-13 03:42:30,263 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 217 rows
2024-07-13 03:43:50,536 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 523 rows
2024-07-13 03:44:03,850 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-13 03:48:33,824 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 291 rows
2024-07-13 03:50:20,892 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 315 rows
2024-07-13 03:50:52,652 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 175 rows
2024-07-13 03:51:48,023 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 258 rows
2024-07-13 03:53:24,143 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 368 rows
2024-07-13 03:55:58,465 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1012 rows
2024-07-13 04:00:29,145 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 311 rows
2024-07-13 04:01:18,270 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 145 rows
2024-07-13 04:01:35,138 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 69 rows
2024-07-13 04:02:18,775 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 239 rows
2024-07-13 04:05:28,093 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1285 rows
2024-07-13 04:05:57,165 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 133 rows
2024-07-13 04:06:05,612 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-13 04:06:36,860 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 179 rows
2024-07-13 04:08:07,204 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 289 rows
2024-07-13 04:09:31,948 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 533 rows
2024-07-13 04:09:53,289 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 73 rows
2024-07-13 04:10:00,020 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-13 04:12:08,123 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 552 rows
2024-07-13 04:12:45,165 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 78 rows
2024-07-13 04:13:06,080 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 72 rows
2024-07-13 04:13:15,344 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-13 04:17:44,077 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 906 rows
2024-07-13 04:20:16,270 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 604 rows
2024-07-13 04:20:23,717 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 43 rows
2024-07-13 04:20:35,682 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-13 04:27:51,335 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 637 rows
2024-07-13 04:28:16,717 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 127 rows
2024-07-13 04:32:43,988 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 950 rows
2024-07-13 04:33:21,341 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 159 rows
2024-07-13 04:35:35,264 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 435 rows
2024-07-13 04:36:44,256 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 161 rows
2024-07-13 04:36:48,763 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-13 04:37:32,845 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 152 rows
2024-07-13 04:37:38,470 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-13 04:38:50,988 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 368 rows
2024-07-13 04:39:01,341 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-13 04:39:22,210 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 123 rows
2024-07-13 04:39:41,666 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 129 rows
2024-07-13 04:39:48,506 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-13 04:40:08,161 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 100 rows
2024-07-13 04:44:09,659 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 988 rows
2024-07-13 04:45:28,759 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 230 rows
2024-07-13 04:45:35,818 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-13 04:46:21,583 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 106 rows
2024-07-13 04:46:30,635 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 34 rows
2024-07-13 04:46:41,670 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-13 04:46:59,492 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 91 rows
2024-07-13 04:48:36,911 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 616 rows
2024-07-13 04:48:53,096 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 25 rows
2024-07-13 04:49:13,266 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 61 rows
2024-07-13 04:49:50,289 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 245 rows
2024-07-13 04:52:49,256 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 696 rows
2024-07-13 04:53:00,634 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-13 04:54:43,967 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 299 rows
2024-07-13 04:55:41,921 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 240 rows
2024-07-13 04:56:12,926 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 126 rows
2024-07-13 04:57:54,510 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 626 rows
2024-07-13 04:59:08,450 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 236 rows
2024-07-13 04:59:56,195 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 211 rows
2024-07-13 05:00:27,043 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 139 rows
2024-07-13 05:01:24,184 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 100 rows
2024-07-13 05:01:29,998 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 18 rows
2024-07-13 05:01:38,129 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-13 05:02:16,914 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 198 rows
2024-07-13 05:03:02,158 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 239 rows
2024-07-13 05:03:45,824 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 108 rows
2024-07-13 05:03:58,572 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-13 05:08:23,551 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 296 rows
2024-07-13 05:08:30,461 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-13 05:09:33,554 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 159 rows
2024-07-13 05:10:47,949 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 207 rows
2024-07-13 05:11:12,767 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 103 rows
2024-07-13 05:12:54,060 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 674 rows
2024-07-13 05:13:26,017 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 204 rows
2024-07-13 05:13:59,051 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 95 rows
2024-07-13 05:14:31,154 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 164 rows
2024-07-13 05:17:28,825 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1060 rows
2024-07-13 05:17:38,520 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-13 05:17:51,966 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-13 05:18:12,456 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 61 rows
2024-07-13 05:18:56,086 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 151 rows
2024-07-13 05:19:34,195 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 76 rows
2024-07-13 05:19:50,915 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 86 rows
2024-07-13 05:19:54,132 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-13 05:20:00,544 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-13 05:22:33,475 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 355 rows
2024-07-13 05:25:31,764 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 644 rows
2024-07-13 05:29:59,427 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 916 rows
2024-07-13 05:35:28,134 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1197 rows
2024-07-13 05:35:41,891 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-13 05:43:40,449 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 683 rows
2024-07-13 05:44:27,000 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 304 rows
2024-07-13 05:44:37,817 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 33 rows
2024-07-13 05:45:41,131 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 295 rows
2024-07-13 05:47:15,771 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 293 rows
2024-07-13 05:47:54,672 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 208 rows
2024-07-13 05:48:35,476 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 174 rows
2024-07-13 05:48:45,556 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-13 05:49:29,766 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 174 rows
2024-07-13 05:49:34,337 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-13 05:49:58,189 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 90 rows
2024-07-13 05:50:09,095 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 50 rows
2024-07-13 05:57:00,147 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1965 rows
2024-07-13 05:57:19,680 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 73 rows
2024-07-13 05:58:27,371 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 335 rows
2024-07-13 05:58:44,347 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 59 rows
2024-07-13 05:59:09,006 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 124 rows
2024-07-13 05:59:23,645 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-13 05:59:41,502 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 91 rows
2024-07-13 05:59:44,705 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-13 05:59:51,254 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-13 06:00:13,673 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 91 rows
2024-07-13 06:00:32,525 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 52 rows
2024-07-13 06:01:13,888 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 286 rows
2024-07-13 06:02:49,272 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 628 rows
2024-07-13 06:03:00,578 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-13 06:03:31,878 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 103 rows
2024-07-13 06:03:42,650 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 49 rows
2024-07-13 06:03:58,208 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 97 rows
2024-07-13 06:04:18,635 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 121 rows
2024-07-13 06:04:25,008 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-13 06:05:06,418 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 215 rows
2024-07-13 06:07:33,668 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 389 rows
2024-07-13 06:07:37,766 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-13 06:09:06,723 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 443 rows
2024-07-13 06:11:08,091 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 450 rows
2024-07-13 06:11:29,820 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 25 rows
2024-07-13 06:12:00,779 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 99 rows
2024-07-13 06:12:07,141 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-13 06:12:16,673 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-13 06:12:20,539 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-13 06:12:26,687 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-13 06:12:48,351 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 37 rows
2024-07-13 06:14:50,052 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 822 rows
2024-07-13 06:19:20,787 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 926 rows
2024-07-13 06:19:49,453 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 150 rows
2024-07-13 06:20:29,657 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 229 rows
2024-07-13 06:26:59,618 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 503 rows
2024-07-13 06:27:18,173 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 74 rows
2024-07-13 06:27:34,835 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 80 rows
2024-07-13 06:30:41,310 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 656 rows
2024-07-13 06:31:14,994 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 159 rows
2024-07-13 06:31:23,013 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-13 06:31:49,339 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 135 rows
2024-07-13 06:31:59,876 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-13 06:32:41,591 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 273 rows
2024-07-13 06:35:45,502 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 638 rows
2024-07-13 06:37:49,924 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 909 rows
2024-07-13 06:38:15,695 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 68 rows
2024-07-13 06:38:36,059 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 69 rows
2024-07-13 06:39:13,361 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 240 rows
2024-07-13 06:39:44,320 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 166 rows
2024-07-13 06:39:51,135 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-13 06:40:38,283 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 202 rows
2024-07-13 06:43:03,953 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 586 rows
2024-07-13 06:47:03,014 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 961 rows
2024-07-13 06:49:39,879 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 587 rows
2024-07-13 06:49:54,020 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-13 06:52:56,115 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 752 rows
2024-07-13 06:57:07,821 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 788 rows
2024-07-13 06:57:29,367 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 77 rows
2024-07-13 07:02:04,102 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 792 rows
2024-07-13 07:02:07,441 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 4 rows
2024-07-13 07:02:11,351 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-13 07:03:13,059 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 329 rows
2024-07-13 07:03:37,638 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 153 rows
2024-07-13 07:05:26,989 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 498 rows
2024-07-13 07:05:48,701 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 111 rows
2024-07-13 07:06:13,587 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 135 rows
2024-07-13 07:08:44,071 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 917 rows
2024-07-13 07:09:21,069 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 203 rows
2024-07-13 07:09:33,098 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 58 rows
2024-07-13 07:09:47,318 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 79 rows
2024-07-13 07:10:18,717 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 156 rows
2024-07-13 07:12:39,645 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 571 rows
2024-07-13 07:12:55,417 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 92 rows
2024-07-13 07:13:07,423 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 57 rows
2024-07-13 07:18:36,845 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 371 rows
2024-07-13 07:19:07,817 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 174 rows
2024-07-13 07:27:07,036 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2032 rows
2024-07-13 07:28:43,800 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 312 rows
2024-07-13 07:31:03,410 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 899 rows
2024-07-13 07:36:32,121 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2407 rows
2024-07-13 07:36:53,434 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 109 rows
2024-07-13 07:38:40,072 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 307 rows
2024-07-13 07:38:53,383 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 64 rows
2024-07-13 07:38:56,603 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-13 07:39:13,037 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 51 rows
2024-07-13 07:40:43,338 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 299 rows
2024-07-13 07:40:46,846 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-13 07:48:08,076 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1784 rows
2024-07-13 07:48:21,883 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 25 rows
2024-07-13 07:48:47,600 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 117 rows
2024-07-13 07:49:46,583 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 279 rows
2024-07-13 07:51:48,944 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 444 rows
2024-07-13 07:52:42,100 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 363 rows
2024-07-13 07:53:20,309 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 228 rows
2024-07-13 07:53:43,009 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 106 rows
2024-07-13 07:56:48,214 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 668 rows
2024-07-13 07:57:50,408 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 179 rows
2024-07-13 07:59:51,404 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 270 rows
2024-07-13 08:00:28,446 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 202 rows
2024-07-13 08:03:38,792 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 685 rows
2024-07-13 08:06:10,035 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1077 rows
2024-07-13 08:09:57,212 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 549 rows
2024-07-13 08:10:43,768 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 109 rows
2024-07-13 08:11:36,785 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 274 rows
2024-07-13 08:13:58,425 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 920 rows
2024-07-13 08:14:47,052 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 211 rows
2024-07-13 08:15:10,300 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 108 rows
2024-07-13 08:16:05,405 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 241 rows
2024-07-13 08:16:27,333 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 115 rows
2024-07-13 08:19:20,082 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 413 rows
2024-07-13 08:19:28,793 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-13 08:20:42,400 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 306 rows
2024-07-13 08:22:03,156 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 198 rows
2024-07-13 08:22:52,703 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 225 rows
2024-07-13 08:23:02,256 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 7 rows
2024-07-13 08:23:20,418 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 91 rows
2024-07-13 08:23:32,115 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 38 rows
2024-07-13 08:28:10,909 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 922 rows
2024-07-13 08:29:09,805 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 133 rows
2024-07-13 08:34:36,570 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2420 rows
2024-07-13 08:34:52,134 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 22 rows
2024-07-13 08:41:12,647 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1735 rows
2024-07-13 08:42:52,974 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 419 rows
2024-07-13 08:43:06,096 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-13 08:51:28,988 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2114 rows
2024-07-13 08:54:48,216 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 535 rows
2024-07-13 08:56:21,199 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 299 rows
2024-07-13 08:56:40,123 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 70 rows
2024-07-13 08:56:53,407 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 67 rows
2024-07-13 08:57:11,966 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 54 rows
2024-07-13 08:57:35,718 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 144 rows
2024-07-13 08:57:54,403 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 120 rows
2024-07-13 08:58:14,516 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 20 rows
2024-07-13 08:58:19,091 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 8 rows
2024-07-13 08:58:39,775 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 67 rows
2024-07-13 08:59:17,998 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 230 rows
2024-07-13 09:00:32,292 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 207 rows
2024-07-13 09:00:35,248 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-13 09:01:01,613 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 71 rows
2024-07-13 09:01:14,660 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-13 09:01:27,553 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 48 rows
2024-07-13 09:03:51,581 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 542 rows
2024-07-13 09:05:14,136 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 259 rows
2024-07-13 09:05:21,697 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-13 09:05:31,278 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 23 rows
2024-07-13 09:06:34,797 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 166 rows
2024-07-13 09:06:48,869 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 52 rows
2024-07-13 09:10:02,150 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 263 rows
2024-07-13 09:13:09,221 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1222 rows
2024-07-13 09:18:05,923 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1020 rows
2024-07-13 09:18:35,354 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 24 rows
2024-07-13 09:18:53,041 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 74 rows
2024-07-13 09:19:18,427 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 26 rows
2024-07-13 09:20:17,441 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 271 rows
2024-07-13 09:20:22,376 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 15 rows
2024-07-13 09:21:05,017 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 293 rows
2024-07-13 09:21:32,615 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 73 rows
2024-07-13 09:27:49,473 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1835 rows
2024-07-13 09:28:11,611 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 17 rows
2024-07-13 09:28:17,633 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 13 rows
2024-07-13 09:28:27,797 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-13 09:28:48,814 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 100 rows
2024-07-13 09:37:11,631 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 2112 rows
2024-07-13 09:37:22,467 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 66 rows
2024-07-13 09:37:43,384 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 21 rows
2024-07-13 09:38:42,167 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 154 rows
2024-07-13 09:38:56,343 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 44 rows
2024-07-13 09:39:01,040 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 6 rows
2024-07-13 09:39:14,914 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 39 rows
2024-07-13 09:45:31,213 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 405 rows
2024-07-13 09:45:48,248 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 84 rows
2024-07-13 09:45:51,313 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 5 rows
2024-07-13 09:48:32,663 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 708 rows
2024-07-13 09:49:30,646 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 321 rows
2024-07-13 09:49:52,224 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 120 rows
2024-07-13 09:50:10,383 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 87 rows
2024-07-13 09:50:19,021 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 43 rows
2024-07-13 09:52:47,240 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 917 rows
2024-07-13 09:53:29,973 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 150 rows
2024-07-13 09:53:46,096 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 110 rows
2024-07-13 09:55:42,915 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 544 rows
2024-07-13 09:57:30,065 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 421 rows
2024-07-13 09:57:47,334 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 53 rows
2024-07-13 10:04:16,891 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 467 rows
2024-07-13 10:04:35,860 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 78 rows
2024-07-13 10:08:56,819 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 936 rows
2024-07-13 10:09:03,280 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 28 rows
2024-07-13 10:10:37,416 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 608 rows
2024-07-13 10:12:35,588 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 702 rows
2024-07-13 10:14:19,513 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 665 rows
2024-07-13 10:14:31,781 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 62 rows
2024-07-13 10:16:17,323 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 498 rows
2024-07-13 10:16:41,931 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-13 10:16:53,124 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 52 rows
2024-07-13 10:17:02,703 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-13 10:17:05,705 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-13 10:17:48,141 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 146 rows
2024-07-13 10:18:11,204 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-13 10:18:18,442 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-13 10:18:38,831 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 94 rows
2024-07-13 10:18:47,330 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-13 10:19:12,636 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 136 rows
2024-07-13 10:19:23,773 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-13 10:19:36,765 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 58 rows
2024-07-13 10:19:44,307 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 14 rows
2024-07-13 10:20:03,669 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 120 rows
2024-07-13 10:20:39,757 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 125 rows
2024-07-13 10:20:53,114 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 31 rows
2024-07-13 10:21:01,808 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 35 rows
2024-07-13 10:21:19,395 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-13 10:21:38,737 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 87 rows
2024-07-13 10:22:39,852 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 160 rows
2024-07-13 10:25:09,697 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 582 rows
2024-07-13 10:25:21,032 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 46 rows
2024-07-13 10:25:29,267 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-13 10:25:43,430 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 51 rows
2024-07-13 10:26:52,486 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 288 rows
2024-07-13 10:28:32,241 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 326 rows
2024-07-13 10:32:21,858 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1677 rows
2024-07-13 10:32:28,619 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-13 10:32:41,954 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 68 rows
2024-07-13 10:35:10,647 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 418 rows
2024-07-13 10:35:28,439 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 65 rows
2024-07-13 10:35:40,314 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 40 rows
2024-07-13 10:35:57,712 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 72 rows
2024-07-13 10:36:09,386 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 14 rows
2024-07-13 10:36:34,233 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 80 rows
2024-07-13 10:37:40,761 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 166 rows
2024-07-13 10:37:54,235 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 9 rows
2024-07-13 10:38:04,289 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-13 10:38:12,078 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 51 rows
2024-07-13 10:41:49,728 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 574 rows
2024-07-13 10:42:10,777 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 65 rows
2024-07-13 10:42:27,619 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 105 rows
2024-07-13 10:42:37,485 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 47 rows
2024-07-13 10:44:54,553 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 884 rows
2024-07-13 10:45:14,835 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 99 rows
2024-07-13 10:50:04,140 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1061 rows
2024-07-13 10:50:13,732 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 16 rows
2024-07-13 10:50:21,274 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 12 rows
2024-07-13 10:56:51,017 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 517 rows
2024-07-13 11:02:20,200 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1219 rows
2024-07-13 11:03:14,968 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 245 rows
2024-07-13 11:03:32,727 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 82 rows
2024-07-13 11:06:56,043 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 168 rows
2024-07-13 11:06:59,127 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 3 rows
2024-07-13 11:07:22,038 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 60 rows
2024-07-13 11:07:43,127 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 54 rows
2024-07-13 11:07:47,945 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 30 rows
2024-07-13 11:08:05,589 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 36 rows
2024-07-13 11:08:17,403 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 11 rows
2024-07-13 11:09:32,012 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 225 rows
2024-07-13 11:13:16,829 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 1207 rows
2024-07-13 11:16:18,622 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 714 rows
2024-07-13 11:16:56,438 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 147 rows
2024-07-13 11:17:12,555 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_delete0703_test with 50 rows
2024-07-13 22:22:46,589 ｜ INFO ｜ milvus_helpers.py ｜ __init__ ｜ 14 ｜ Successfully connect to Milvus with IP:************* and PORT:19530
2024-07-13 22:22:46,794 ｜ INFO ｜ milvus_helpers.py ｜ create_collection ｜ 52 ｜ Create Milvus collection: productQA_all0713
2024-07-13 22:22:47,491 ｜ INFO ｜ milvus_helpers.py ｜ create_index ｜ 107 ｜ Successfully create index in collection:productQA_all0713 with param:{'index_type': 'IVF_FLAT', 'metric_type': 'COSINE', 'params': {'nlist': 16384}}
2024-07-13 22:22:52,736 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:22:52.558394', 'RPC error': '2024-07-13 22:22:52.736757'}>
2024-07-13 22:22:52,737 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:22:54,952 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:22:54.952156', 'RPC error': '2024-07-13 22:22:54.952346'}>
2024-07-13 22:22:54,952 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:22:56,317 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:22:56,606 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:22:59,386 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:22:59.386269', 'RPC error': '2024-07-13 22:22:59.386424'}>
2024-07-13 22:22:59,386 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:23:04,209 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:23:21,426 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:23:21.426302', 'RPC error': '2024-07-13 22:23:21.426505'}>
2024-07-13 22:23:21,427 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:23:22,127 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:23:22,625 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:23:22.625118', 'RPC error': '2024-07-13 22:23:22.625349'}>
2024-07-13 22:23:22,625 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:23:23,764 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:23:29,058 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:23:29.058229', 'RPC error': '2024-07-13 22:23:29.058413'}>
2024-07-13 22:23:29,058 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:23:40,132 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:23:40.132299', 'RPC error': '2024-07-13 22:23:40.132510'}>
2024-07-13 22:23:40,132 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:23:43,125 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:23:45,590 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:23:45.590775', 'RPC error': '2024-07-13 22:23:45.590938'}>
2024-07-13 22:23:45,591 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:23:45,937 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:23:50,754 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:24:05,024 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:24:05.024751', 'RPC error': '2024-07-13 22:24:05.024932'}>
2024-07-13 22:24:05,025 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:24:12,216 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:24:12.216277', 'RPC error': '2024-07-13 22:24:12.216423'}>
2024-07-13 22:24:12,216 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:24:12,626 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:24:17,495 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:24:22,458 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:24:23,271 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:24:24,480 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:24:25,028 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:24:25.028659', 'RPC error': '2024-07-13 22:24:25.028810'}>
2024-07-13 22:24:25,029 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:24:32,694 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:24:32.694186', 'RPC error': '2024-07-13 22:24:32.694368'}>
2024-07-13 22:24:32,694 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:24:33,556 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:24:38,509 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:24:48,594 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:24:48.594409', 'RPC error': '2024-07-13 22:24:48.594602'}>
2024-07-13 22:24:48,594 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:24:48,991 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:24:49,302 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:24:51,668 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:24:53,130 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:24:53.129968', 'RPC error': '2024-07-13 22:24:53.130130'}>
2024-07-13 22:24:53,130 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:24:53,468 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:24:53.467947', 'RPC error': '2024-07-13 22:24:53.468097'}>
2024-07-13 22:24:53,468 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:24:58,329 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:24:58.329529', 'RPC error': '2024-07-13 22:24:58.329709'}>
2024-07-13 22:24:58,330 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:25:02,398 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:25:02.398340', 'RPC error': '2024-07-13 22:25:02.398535'}>
2024-07-13 22:25:02,398 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:25:07,326 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:25:18,461 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:25:25,237 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:25:25.236905', 'RPC error': '2024-07-13 22:25:25.237097'}>
2024-07-13 22:25:25,237 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:25:26,608 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:25:26.608343', 'RPC error': '2024-07-13 22:25:26.608516'}>
2024-07-13 22:25:26,608 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:25:33,882 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:25:34,306 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:25:34,645 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:25:39,614 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:25:51,628 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:25:51.628344', 'RPC error': '2024-07-13 22:25:51.628578'}>
2024-07-13 22:25:51,629 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:25:56,721 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:26:01,676 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:26:01.676640', 'RPC error': '2024-07-13 22:26:01.676813'}>
2024-07-13 22:26:01,677 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:26:06,331 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:26:06.330918', 'RPC error': '2024-07-13 22:26:06.331091'}>
2024-07-13 22:26:06,331 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:26:07,762 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:26:07.762097', 'RPC error': '2024-07-13 22:26:07.762270'}>
2024-07-13 22:26:07,762 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:26:08,257 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:26:08.257225', 'RPC error': '2024-07-13 22:26:08.257418'}>
2024-07-13 22:26:08,257 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:26:08,917 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:26:08.917710', 'RPC error': '2024-07-13 22:26:08.917877'}>
2024-07-13 22:26:08,918 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:26:12,420 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:26:12.420710', 'RPC error': '2024-07-13 22:26:12.420898'}>
2024-07-13 22:26:12,421 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:26:15,247 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:26:19,853 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:26:19.853700', 'RPC error': '2024-07-13 22:26:19.853899'}>
2024-07-13 22:26:19,854 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:26:24,727 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:26:24.727082', 'RPC error': '2024-07-13 22:26:24.727232'}>
2024-07-13 22:26:24,727 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:26:29,673 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:26:32,447 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:26:32,773 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:26:33,576 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:26:38,610 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:26:38,909 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:26:41,939 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:26:42,337 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:26:42.337319', 'RPC error': '2024-07-13 22:26:42.337531'}>
2024-07-13 22:26:42,337 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:26:54,491 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:26:54.491493', 'RPC error': '2024-07-13 22:26:54.491701'}>
2024-07-13 22:26:54,492 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:26:59,705 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:27:03,990 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:27:03.990290', 'RPC error': '2024-07-13 22:27:03.990434'}>
2024-07-13 22:27:03,990 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:27:09,022 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:27:14,226 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:27:15,444 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:27:15.444788', 'RPC error': '2024-07-13 22:27:15.444945'}>
2024-07-13 22:27:15,445 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:27:16,234 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:27:16.234465', 'RPC error': '2024-07-13 22:27:16.234623'}>
2024-07-13 22:27:16,234 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:27:21,220 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:27:21,526 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:27:25,052 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:27:25,439 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:27:30,593 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:27:35,722 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:27:40,896 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:27:44,522 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:27:44,822 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:27:45,721 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:27:46,365 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:27:46.365370', 'RPC error': '2024-07-13 22:27:46.365567'}>
2024-07-13 22:27:46,365 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:27:51,429 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:27:56,556 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:28:05,298 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:28:05.298646', 'RPC error': '2024-07-13 22:28:05.298833'}>
2024-07-13 22:28:05,299 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:28:05,606 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:28:06,158 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:28:08,142 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:28:08.142327', 'RPC error': '2024-07-13 22:28:08.142495'}>
2024-07-13 22:28:08,142 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:28:08,975 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:28:09,283 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:28:11,022 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:28:11.022023', 'RPC error': '2024-07-13 22:28:11.022226'}>
2024-07-13 22:28:11,022 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:28:16,083 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_all0713 with 2 rows
2024-07-13 22:28:22,372 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:28:22.372109', 'RPC error': '2024-07-13 22:28:22.372288'}>
2024-07-13 22:28:22,372 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:28:30,072 ｜ ERROR ｜ decorators.py ｜ handler ｜ 146 ｜ RPC error: [batch_insert], <ParamError: (code=1, message=expect string input, got: <class 'int'>)>, <Time:{'RPC start': '2024-07-13 22:28:30.072711', 'RPC error': '2024-07-13 22:28:30.072922'}>
2024-07-13 22:28:30,073 ｜ ERROR ｜ milvus_helpers.py ｜ insert_doc ｜ 88 ｜ Failed to insert data to Milvus: <ParamError: (code=1, message=expect string input, got: <class 'int'>)>
2024-07-13 22:47:27,034 ｜ INFO ｜ milvus_helpers.py ｜ __init__ ｜ 14 ｜ Successfully connect to Milvus with IP:************* and PORT:19530
2024-07-13 22:47:27,176 ｜ INFO ｜ milvus_helpers.py ｜ create_collection ｜ 52 ｜ Create Milvus collection: productQA_qingxi
2024-07-13 22:47:27,854 ｜ INFO ｜ milvus_helpers.py ｜ create_index ｜ 107 ｜ Successfully create index in collection:productQA_qingxi with param:{'index_type': 'IVF_FLAT', 'metric_type': 'COSINE', 'params': {'nlist': 16384}}
2024-07-13 22:47:32,466 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 22 rows
2024-07-13 22:47:34,738 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 26 rows
2024-07-13 22:47:36,036 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:47:36,325 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:47:39,165 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 30 rows
2024-07-13 22:47:44,057 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:48:01,849 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 62 rows
2024-07-13 22:48:02,572 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:48:03,016 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 22:48:04,119 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:48:09,607 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 56 rows
2024-07-13 22:48:21,445 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 95 rows
2024-07-13 22:48:24,381 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:48:27,223 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 29 rows
2024-07-13 22:48:27,547 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:48:32,358 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:48:48,589 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 148 rows
2024-07-13 22:48:56,070 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 30 rows
2024-07-13 22:48:56,380 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:49:01,440 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:49:06,625 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:49:07,481 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:49:08,580 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:49:09,167 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:49:18,577 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 61 rows
2024-07-13 22:49:19,365 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:49:24,362 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:49:35,742 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 96 rows
2024-07-13 22:49:36,059 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:49:37,496 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:49:40,138 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:49:41,994 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 15 rows
2024-07-13 22:49:42,541 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 22:49:48,401 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 56 rows
2024-07-13 22:49:53,055 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 53 rows
2024-07-13 22:49:58,066 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:50:09,233 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:50:16,822 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 45 rows
2024-07-13 22:50:18,414 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 15 rows
2024-07-13 22:50:25,832 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:50:26,145 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:50:26,452 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:50:31,561 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:50:45,353 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 154 rows
2024-07-13 22:50:50,413 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:50:55,915 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 40 rows
2024-07-13 22:51:01,134 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 37 rows
2024-07-13 22:51:02,863 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 14 rows
2024-07-13 22:51:03,513 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 6 rows
2024-07-13 22:51:04,226 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 8 rows
2024-07-13 22:51:08,598 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 45 rows
2024-07-13 22:51:11,581 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:51:16,946 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 65 rows
2024-07-13 22:51:22,628 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 58 rows
2024-07-13 22:51:27,629 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:51:30,361 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:51:30,664 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:51:31,596 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:51:36,524 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:51:36,831 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:51:39,906 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:51:40,341 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:51:56,383 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 154 rows
2024-07-13 22:52:01,576 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:52:06,116 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 26 rows
2024-07-13 22:52:11,149 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:52:16,232 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:52:17,620 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 13 rows
2024-07-13 22:52:18,381 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 22:52:23,436 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:52:23,761 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:52:27,490 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:52:27,894 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:52:33,003 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:52:38,114 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:52:43,203 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:52:46,979 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:52:47,291 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:52:48,159 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:52:48,891 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 5 rows
2024-07-13 22:52:54,038 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:52:59,170 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:53:09,589 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 113 rows
2024-07-13 22:53:09,907 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:53:10,471 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:53:12,689 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 24 rows
2024-07-13 22:53:13,545 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:53:13,856 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:53:15,696 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 5 rows
2024-07-13 22:53:20,916 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:53:28,598 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 23 rows
2024-07-13 22:53:36,718 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 32 rows
2024-07-13 22:53:45,899 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 85 rows
2024-07-13 22:53:47,449 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:53:52,547 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:53:54,252 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 19 rows
2024-07-13 22:54:06,836 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 131 rows
2024-07-13 22:54:09,246 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:54:22,544 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 122 rows
2024-07-13 22:54:30,843 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 93 rows
2024-07-13 22:54:38,174 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:54:47,646 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 98 rows
2024-07-13 22:54:48,327 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 22:54:53,461 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:54:55,586 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 3 rows
2024-07-13 22:54:57,333 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 18 rows
2024-07-13 22:55:02,427 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:55:04,320 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 17 rows
2024-07-13 22:55:05,033 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:55:09,936 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:55:11,505 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:55:20,590 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 84 rows
2024-07-13 22:55:20,892 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:55:41,200 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 219 rows
2024-07-13 22:55:53,048 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 123 rows
2024-07-13 22:55:53,355 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:56:02,466 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 109 rows
2024-07-13 22:56:07,488 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:56:12,563 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:56:24,481 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:56:28,706 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 50 rows
2024-07-13 22:56:34,217 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 58 rows
2024-07-13 22:56:35,389 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:56:43,591 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 78 rows
2024-07-13 22:56:45,342 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:56:47,052 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 17 rows
2024-07-13 22:56:47,913 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 7 rows
2024-07-13 22:56:52,833 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:56:54,303 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 13 rows
2024-07-13 22:56:59,433 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:57:04,595 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:57:05,713 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 7 rows
2024-07-13 22:57:07,969 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 17 rows
2024-07-13 22:57:13,138 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:57:17,127 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:57:18,390 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:57:35,196 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 19 rows
2024-07-13 22:57:40,224 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:57:45,713 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 60 rows
2024-07-13 22:57:46,437 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 22:57:51,689 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:57:56,841 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:57:58,130 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:57:58,644 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:58:00,613 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 18 rows
2024-07-13 22:58:00,996 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:58:02,512 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:58:03,207 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 5 rows
2024-07-13 22:58:04,685 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 13 rows
2024-07-13 22:58:05,005 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:58:06,311 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 7 rows
2024-07-13 22:58:06,617 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:58:10,054 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 35 rows
2024-07-13 22:58:15,147 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:58:20,180 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:58:25,321 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:58:25,894 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 22:58:46,158 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 216 rows
2024-07-13 22:58:51,147 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:58:51,581 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:58:52,563 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:58:59,362 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 3 rows
2024-07-13 22:59:01,073 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:59:05,530 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 3 rows
2024-07-13 22:59:08,732 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:59:11,293 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:59:12,479 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:59:12,776 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:59:14,395 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 14 rows
2024-07-13 22:59:19,720 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:59:20,039 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:59:32,430 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:59:37,682 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:59:44,413 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 25 rows
2024-07-13 22:59:49,559 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:59:52,660 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 36 rows
2024-07-13 22:59:57,719 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 22:59:58,054 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:00:03,301 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:00:04,989 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 13 rows
2024-07-13 23:00:10,205 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:00:15,435 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:00:15,746 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:00:17,310 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:00:18,691 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 13 rows
2024-07-13 23:00:18,996 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:00:29,740 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 118 rows
2024-07-13 23:00:30,450 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 7 rows
2024-07-13 23:00:43,424 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 124 rows
2024-07-13 23:00:44,710 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 11 rows
2024-07-13 23:00:45,153 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:00:45,791 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:00:46,621 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:00:51,693 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 61 rows
2024-07-13 23:00:52,406 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:00:55,304 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 26 rows
2024-07-13 23:00:55,634 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:01:00,705 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:01:02,981 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:01:04,489 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:01:09,661 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:01:12,846 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 37 rows
2024-07-13 23:01:18,022 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:01:18,343 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:01:22,635 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 23:01:27,189 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 47 rows
2024-07-13 23:01:27,547 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:01:32,715 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:01:33,042 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:01:38,184 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:01:41,527 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 20 rows
2024-07-13 23:01:43,470 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 18 rows
2024-07-13 23:01:45,029 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:01:52,795 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 65 rows
2024-07-13 23:01:57,876 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:02:03,150 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:02:03,588 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:02:09,104 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 65 rows
2024-07-13 23:02:14,218 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:02:14,631 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:02:19,647 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:02:21,200 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 17 rows
2024-07-13 23:02:22,350 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:02:22,656 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:02:24,180 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:02:29,362 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:02:29,675 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:02:32,817 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 34 rows
2024-07-13 23:02:38,987 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 51 rows
2024-07-13 23:02:39,291 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:02:41,924 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 28 rows
2024-07-13 23:02:47,259 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 63 rows
2024-07-13 23:02:48,148 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 5 rows
2024-07-13 23:02:53,222 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:02:55,223 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:02:58,287 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 28 rows
2024-07-13 23:03:03,354 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:03:06,499 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:03:06,811 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:03:11,925 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:03:13,515 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:03:15,571 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 24 rows
2024-07-13 23:03:55,897 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 340 rows
2024-07-13 23:03:57,631 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:04:50,643 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 304 rows
2024-07-13 23:04:59,555 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:05:02,474 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 35 rows
2024-07-13 23:05:16,115 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:05:21,266 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:05:24,213 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 32 rows
2024-07-13 23:05:29,197 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 23:07:34,944 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 943 rows
2024-07-13 23:07:35,265 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:07:40,397 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:07:42,244 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:07:44,496 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:07:44,938 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:07:49,994 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:07:55,061 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:07:58,590 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 36 rows
2024-07-13 23:07:59,130 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:07:59,605 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 23:08:00,193 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 3 rows
2024-07-13 23:08:08,492 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 60 rows
2024-07-13 23:08:09,475 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:08:11,396 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:08:16,204 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 50 rows
2024-07-13 23:08:21,260 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:08:26,381 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:08:30,521 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 33 rows
2024-07-13 23:08:30,947 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:08:36,695 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:08:39,451 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 30 rows
2024-07-13 23:08:44,580 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:08:45,167 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:08:47,137 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:08:51,558 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:09:03,745 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 120 rows
2024-07-13 23:09:04,123 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:09:04,430 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:09:09,495 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:09:10,937 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:09:18,848 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 84 rows
2024-07-13 23:09:47,874 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 92 rows
2024-07-13 23:09:53,329 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 53 rows
2024-07-13 23:09:54,149 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:09:55,740 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:10:00,157 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:10:01,406 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:10:06,483 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:10:11,291 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 59 rows
2024-07-13 23:10:21,149 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 112 rows
2024-07-13 23:10:26,108 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:10:27,359 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:10:28,273 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:10:28,589 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:10:30,139 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:10:33,013 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 25 rows
2024-07-13 23:10:35,951 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:10:41,044 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:10:43,170 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:10:43,494 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:10:48,682 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:10:53,852 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:10:58,362 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 54 rows
2024-07-13 23:11:02,587 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 35 rows
2024-07-13 23:11:04,788 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 20 rows
2024-07-13 23:11:05,199 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:11:05,545 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:11:09,419 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:11:15,776 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:11:20,883 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:11:42,189 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 125 rows
2024-07-13 23:11:42,529 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:12:02,025 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 216 rows
2024-07-13 23:12:07,301 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:12:07,734 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:12:12,928 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:12:13,286 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:12:18,403 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:12:23,506 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:12:28,647 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:12:33,733 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:12:35,859 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:12:57,932 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 174 rows
2024-07-13 23:13:03,612 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:13:05,144 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 16 rows
2024-07-13 23:13:08,137 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 20 rows
2024-07-13 23:13:09,320 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:13:14,396 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:13:14,744 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:13:19,821 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:13:20,975 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:13:26,202 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:13:31,380 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:13:34,658 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:13:42,254 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 85 rows
2024-07-13 23:13:43,106 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:13:45,562 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 15 rows
2024-07-13 23:13:48,170 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 24 rows
2024-07-13 23:13:48,724 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 23:13:51,874 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 36 rows
2024-07-13 23:13:54,099 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:13:54,418 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:13:58,519 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:14:03,679 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:14:04,571 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:14:09,298 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 59 rows
2024-07-13 23:14:14,475 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:14:14,802 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:14:19,870 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:14:20,196 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:14:23,245 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 25 rows
2024-07-13 23:14:26,995 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 37 rows
2024-07-13 23:14:32,162 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 23:14:33,434 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:14:34,409 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:14:34,714 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:14:35,560 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:14:40,648 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:14:53,607 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 148 rows
2024-07-13 23:14:53,962 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:14:54,319 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:14:59,251 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:14:59,694 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:15:00,066 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:15:01,918 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:15:13,429 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 110 rows
2024-07-13 23:15:18,416 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:15:23,526 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:15:31,435 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 66 rows
2024-07-13 23:15:32,052 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:15:33,304 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:15:35,495 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 22 rows
2024-07-13 23:15:39,070 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 35 rows
2024-07-13 23:15:42,771 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:15:44,980 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:15:47,839 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 20 rows
2024-07-13 23:15:52,963 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:16:05,354 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 136 rows
2024-07-13 23:16:05,736 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:16:10,853 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:16:11,415 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:16:11,741 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:16:16,688 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:16:17,500 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:16:22,582 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:16:27,706 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:16:30,004 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:16:31,400 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 9 rows
2024-07-13 23:16:32,354 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 5 rows
2024-07-13 23:16:37,456 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:16:38,627 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:16:49,242 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 104 rows
2024-07-13 23:16:54,453 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:17:02,576 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 85 rows
2024-07-13 23:17:02,891 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:17:03,371 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:17:09,498 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 45 rows
2024-07-13 23:17:15,662 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 47 rows
2024-07-13 23:17:15,984 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:17:28,599 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 146 rows
2024-07-13 23:17:30,682 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:17:33,340 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 23 rows
2024-07-13 23:17:34,018 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:17:38,948 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:17:42,980 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 24 rows
2024-07-13 23:17:44,454 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 14 rows
2024-07-13 23:17:49,655 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:17:54,851 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:17:55,723 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:17:57,003 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:18:03,112 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 62 rows
2024-07-13 23:18:12,137 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 44 rows
2024-07-13 23:18:17,304 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:18:17,751 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:18:22,040 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:18:22,357 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:18:22,670 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:18:27,881 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:18:33,060 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:18:33,511 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:18:34,918 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 13 rows
2024-07-13 23:18:35,234 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:18:59,805 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 184 rows
2024-07-13 23:19:05,000 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:19:10,228 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:19:15,355 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:19:16,222 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:19:21,432 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:19:21,874 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:19:23,820 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:19:27,034 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 32 rows
2024-07-13 23:19:30,000 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:19:34,195 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 49 rows
2024-07-13 23:19:35,696 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 5 rows
2024-07-13 23:19:40,642 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 41 rows
2024-07-13 23:19:45,684 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:19:50,970 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:19:51,371 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:19:52,051 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 5 rows
2024-07-13 23:19:52,451 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:19:56,433 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 39 rows
2024-07-13 23:20:01,512 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:20:06,701 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:20:07,111 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:20:08,131 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:20:08,862 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:20:09,498 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 5 rows
2024-07-13 23:20:24,623 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:20:25,131 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:20:25,724 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 23:20:34,068 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:20:39,249 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:20:50,317 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 119 rows
2024-07-13 23:20:50,631 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:20:55,701 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:20:56,104 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:20:56,571 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:20:56,887 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:21:01,964 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:21:09,424 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:21:11,506 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:21:17,655 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 59 rows
2024-07-13 23:21:18,033 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:21:25,836 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:21:31,222 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:21:36,324 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:21:41,570 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:21:46,825 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:21:51,937 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:21:54,386 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:22:07,431 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 131 rows
2024-07-13 23:22:11,621 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:22:24,641 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 125 rows
2024-07-13 23:22:29,853 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:22:36,772 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 71 rows
2024-07-13 23:22:37,589 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 7 rows
2024-07-13 23:22:37,912 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:22:40,116 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:22:43,527 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 27 rows
2024-07-13 23:22:48,696 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:22:50,435 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 11 rows
2024-07-13 23:22:50,829 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:22:55,948 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:23:21,633 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 227 rows
2024-07-13 23:23:22,168 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 23:23:22,786 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:23:23,243 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:23:25,150 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 19 rows
2024-07-13 23:23:30,258 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:23:42,468 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 122 rows
2024-07-13 23:23:47,406 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:23:52,579 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:23:57,868 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:23:58,187 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:24:03,566 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:24:06,290 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 18 rows
2024-07-13 23:24:11,522 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:24:11,966 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:24:17,150 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:24:17,512 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:24:19,972 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 23 rows
2024-07-13 23:24:20,386 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 3 rows
2024-07-13 23:24:25,538 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:26:00,086 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 817 rows
2024-07-13 23:26:04,217 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 32 rows
2024-07-13 23:26:09,380 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:26:09,722 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:26:10,031 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:26:13,345 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 35 rows
2024-07-13 23:26:18,304 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 61 rows
2024-07-13 23:26:23,452 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:26:24,270 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:26:26,162 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:26:28,459 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:26:33,591 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:26:38,824 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:26:41,277 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 20 rows
2024-07-13 23:27:51,160 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 542 rows
2024-07-13 23:27:51,985 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:27:52,875 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:27:54,249 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:28:06,150 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 139 rows
2024-07-13 23:28:11,228 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:28:16,340 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:28:17,872 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 8 rows
2024-07-13 23:28:19,401 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 13 rows
2024-07-13 23:28:19,708 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:28:21,237 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 7 rows
2024-07-13 23:28:29,501 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 84 rows
2024-07-13 23:28:34,550 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:28:39,689 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:28:42,239 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 13 rows
2024-07-13 23:28:42,560 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:28:59,042 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 160 rows
2024-07-13 23:29:04,191 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:29:06,595 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 21 rows
2024-07-13 23:29:08,232 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:29:13,415 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:29:18,595 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:29:19,129 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:29:19,608 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 23:29:21,160 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:29:26,381 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:29:26,928 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:29:27,552 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:29:27,894 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:29:28,188 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:29:47,557 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 141 rows
2024-07-13 23:29:48,398 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 6 rows
2024-07-13 23:29:54,246 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:29:59,337 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:30:01,838 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 25 rows
2024-07-13 23:30:02,172 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:30:02,721 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:30:11,015 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 76 rows
2024-07-13 23:30:15,940 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 47 rows
2024-07-13 23:30:17,611 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:30:22,746 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:30:46,485 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 148 rows
2024-07-13 23:30:51,638 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:30:52,549 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:30:54,589 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:30:58,996 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 48 rows
2024-07-13 23:30:59,477 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:31:01,095 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:31:01,483 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:31:02,051 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:31:04,154 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 10 rows
2024-07-13 23:31:04,449 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:31:09,647 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:31:14,843 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:31:18,718 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:31:23,844 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:31:24,320 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:31:24,763 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:31:28,199 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:31:33,393 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:31:38,558 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:31:39,003 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:31:42,208 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:31:43,611 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:31:44,369 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 3 rows
2024-07-13 23:31:45,262 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 6 rows
2024-07-13 23:31:46,648 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 14 rows
2024-07-13 23:31:51,763 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:32:48,492 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 277 rows
2024-07-13 23:32:50,606 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 19 rows
2024-07-13 23:32:55,732 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:32:57,564 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 13 rows
2024-07-13 23:32:59,379 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:33:10,914 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 113 rows
2024-07-13 23:33:15,929 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:33:16,299 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:33:18,967 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 23 rows
2024-07-13 23:33:20,789 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 17 rows
2024-07-13 23:33:59,830 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 208 rows
2024-07-13 23:34:01,885 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 10 rows
2024-07-13 23:34:32,452 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 90 rows
2024-07-13 23:34:42,590 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 115 rows
2024-07-13 23:34:43,800 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:34:49,720 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 45 rows
2024-07-13 23:34:50,805 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:34:51,273 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 23:34:51,694 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:34:52,944 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:34:57,890 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:35:11,856 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 130 rows
2024-07-13 23:35:12,165 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:35:17,353 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:35:20,270 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 32 rows
2024-07-13 23:36:08,930 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 231 rows
2024-07-13 23:36:15,531 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 53 rows
2024-07-13 23:36:20,610 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:36:29,446 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 90 rows
2024-07-13 23:36:33,451 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 39 rows
2024-07-13 23:36:48,811 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:36:49,846 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:36:51,436 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 12 rows
2024-07-13 23:36:53,364 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 20 rows
2024-07-13 23:36:53,817 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:36:59,038 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:37:10,270 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 124 rows
2024-07-13 23:37:20,553 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 23:37:21,012 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:37:26,116 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:37:37,132 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 100 rows
2024-07-13 23:37:38,485 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:37:39,407 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 6 rows
2024-07-13 23:37:44,595 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:37:45,841 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:37:51,059 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:37:56,300 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:37:56,616 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:37:57,084 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:38:00,019 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 30 rows
2024-07-13 23:38:01,225 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:38:06,465 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:38:11,697 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:38:16,832 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:38:22,136 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:39:02,149 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 407 rows
2024-07-13 23:39:23,863 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 83 rows
2024-07-13 23:39:25,421 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:39:30,546 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:39:32,381 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:39:50,542 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 201 rows
2024-07-13 23:39:51,055 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 3 rows
2024-07-13 23:39:55,394 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 40 rows
2024-07-13 23:39:55,940 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 23:40:00,694 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 43 rows
2024-07-13 23:40:01,211 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 23:40:06,515 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 23:40:07,134 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:40:07,998 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:40:08,974 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:40:13,015 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:40:14,638 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:40:15,151 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:40:15,502 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:40:40,714 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 129 rows
2024-07-13 23:40:45,910 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:40:51,132 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:40:51,638 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:40:55,776 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:40:56,742 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 9 rows
2024-07-13 23:41:02,114 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 66 rows
2024-07-13 23:41:08,961 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 77 rows
2024-07-13 23:41:10,695 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:41:12,074 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 9 rows
2024-07-13 23:41:12,396 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:41:14,159 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:41:14,477 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:41:19,662 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:41:24,791 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:41:26,376 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:41:31,545 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 23:41:44,565 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 150 rows
2024-07-13 23:41:45,303 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 6 rows
2024-07-13 23:41:46,482 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 9 rows
2024-07-13 23:41:46,822 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:41:49,732 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 33 rows
2024-07-13 23:41:52,717 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 28 rows
2024-07-13 23:41:57,867 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:41:58,830 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 9 rows
2024-07-13 23:42:07,186 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 88 rows
2024-07-13 23:42:10,497 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 26 rows
2024-07-13 23:42:15,584 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:42:20,698 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 37 rows
2024-07-13 23:42:21,016 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:42:23,566 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 20 rows
2024-07-13 23:42:24,210 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:42:24,534 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:42:28,201 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 24 rows
2024-07-13 23:42:29,733 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:42:30,054 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:42:31,609 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:42:36,573 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:42:41,841 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:42:42,466 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 5 rows
2024-07-13 23:42:50,229 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 66 rows
2024-07-13 23:42:55,330 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:43:00,485 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:43:03,268 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:43:14,270 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 124 rows
2024-07-13 23:43:14,683 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:43:21,074 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 9 rows
2024-07-13 23:43:26,248 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:43:28,194 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 23:43:37,193 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 75 rows
2024-07-13 23:43:42,781 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 18 rows
2024-07-13 23:43:43,209 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:43:45,908 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 26 rows
2024-07-13 23:43:47,458 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 17 rows
2024-07-13 23:43:48,351 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:43:48,664 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:43:50,244 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:43:52,341 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 23:44:04,663 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:44:09,814 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:44:11,562 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:44:12,902 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:44:15,132 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 17 rows
2024-07-13 23:44:20,255 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:44:25,397 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:44:26,926 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 13 rows
2024-07-13 23:44:32,049 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:44:37,235 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:44:38,025 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:44:50,507 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 144 rows
2024-07-13 23:44:52,315 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 23:44:57,547 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:45:02,636 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:45:07,181 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 46 rows
2024-07-13 23:45:08,329 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:45:09,960 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:45:10,731 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:45:11,047 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:45:21,866 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 83 rows
2024-07-13 23:45:22,180 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:45:22,490 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:45:25,948 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 36 rows
2024-07-13 23:45:26,380 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:45:27,957 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:45:28,304 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:45:28,615 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:45:30,792 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 22 rows
2024-07-13 23:45:31,106 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:45:31,414 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:45:32,647 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 13 rows
2024-07-13 23:45:33,542 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:45:36,549 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 32 rows
2024-07-13 23:45:41,624 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:45:42,103 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 23:45:42,730 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:45:43,467 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 5 rows
2024-07-13 23:45:44,776 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:45:49,903 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:45:52,139 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:46:06,933 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 89 rows
2024-07-13 23:46:07,541 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 23:46:12,512 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:46:13,001 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:46:18,505 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 65 rows
2024-07-13 23:46:19,505 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:46:20,107 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:46:20,437 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:46:32,366 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 137 rows
2024-07-13 23:46:37,348 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:46:41,013 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 8 rows
2024-07-13 23:46:41,846 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:46:46,861 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:46:47,183 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:46:52,286 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:46:54,274 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 13 rows
2024-07-13 23:47:08,814 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:47:17,151 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 81 rows
2024-07-13 23:47:19,847 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:47:21,012 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 10 rows
2024-07-13 23:47:25,826 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 58 rows
2024-07-13 23:47:26,284 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:47:29,165 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:47:32,051 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 33 rows
2024-07-13 23:47:37,263 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:47:40,644 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:47:45,886 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:47:54,409 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 85 rows
2024-07-13 23:47:57,733 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 25 rows
2024-07-13 23:47:58,581 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:48:01,678 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 34 rows
2024-07-13 23:48:03,136 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 11 rows
2024-07-13 23:48:04,244 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:48:04,856 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 5 rows
2024-07-13 23:48:05,188 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:48:05,627 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 23:48:10,718 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:48:11,027 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:48:11,344 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:48:16,555 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:48:17,085 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:48:22,695 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:48:33,146 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 122 rows
2024-07-13 23:48:36,387 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 33 rows
2024-07-13 23:48:36,712 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:48:41,800 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:48:42,115 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:48:47,159 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:48:47,483 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:48:48,155 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 5 rows
2024-07-13 23:48:48,447 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:48:50,019 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:48:50,325 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:48:55,283 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:48:58,573 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:49:00,164 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:49:00,833 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:49:01,141 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:49:02,687 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:49:09,245 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 55 rows
2024-07-13 23:49:14,416 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:49:19,511 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:49:20,026 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:49:25,121 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:49:26,016 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:49:28,864 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 14 rows
2024-07-13 23:49:29,518 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 5 rows
2024-07-13 23:49:31,329 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 10 rows
2024-07-13 23:49:36,102 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 6 rows
2024-07-13 23:49:45,648 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 98 rows
2024-07-13 23:49:46,929 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:49:47,399 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:49:48,376 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:49:52,751 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 36 rows
2024-07-13 23:49:53,576 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:49:54,190 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 23:49:58,002 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 37 rows
2024-07-13 23:50:02,640 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 59 rows
2024-07-13 23:50:07,797 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:50:13,020 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:50:14,309 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 12 rows
2024-07-13 23:50:19,404 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:50:19,738 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:50:20,241 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:50:25,236 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:50:27,326 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:50:32,533 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:50:44,161 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 131 rows
2024-07-13 23:50:44,770 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:50:47,400 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 30 rows
2024-07-13 23:50:47,867 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:50:48,267 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:50:56,622 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 86 rows
2024-07-13 23:52:37,147 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 848 rows
2024-07-13 23:52:42,139 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:52:42,447 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:52:47,527 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:52:50,265 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 29 rows
2024-07-13 23:52:55,474 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:53:00,538 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:53:05,628 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:53:10,859 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:53:11,236 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:53:23,024 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 82 rows
2024-07-13 23:53:27,993 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:53:32,809 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 37 rows
2024-07-13 23:53:33,802 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:53:37,685 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 33 rows
2024-07-13 23:53:39,861 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 23 rows
2024-07-13 23:53:45,335 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 57 rows
2024-07-13 23:53:47,946 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 25 rows
2024-07-13 23:53:48,515 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:53:53,766 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:53:55,273 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 8 rows
2024-07-13 23:53:58,140 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 3 rows
2024-07-13 23:54:03,464 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:54:08,600 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:54:10,765 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:54:16,043 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:54:21,608 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 54 rows
2024-07-13 23:54:24,820 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 30 rows
2024-07-13 23:54:31,506 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 72 rows
2024-07-13 23:54:34,275 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 27 rows
2024-07-13 23:54:34,747 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:54:35,424 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:54:35,916 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:54:40,979 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:54:46,069 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:54:49,584 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:54:50,860 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:54:51,219 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:54:58,658 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 50 rows
2024-07-13 23:55:00,508 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:55:00,816 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:55:01,628 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:55:01,961 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:55:44,422 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 410 rows
2024-07-13 23:55:49,479 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:55:56,444 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 71 rows
2024-07-13 23:55:56,865 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:55:57,279 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:56:02,403 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:56:07,562 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:56:15,491 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 69 rows
2024-07-13 23:56:17,248 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 20 rows
2024-07-13 23:56:20,180 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 10 rows
2024-07-13 23:56:29,820 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 114 rows
2024-07-13 23:56:31,387 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:56:36,282 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:56:37,821 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:56:38,357 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:56:43,535 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:56:44,172 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 6 rows
2024-07-13 23:56:49,340 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:56:49,666 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:56:51,480 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 10 rows
2024-07-13 23:56:53,550 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:56:54,427 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:56:54,904 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 23:56:55,345 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:56:56,241 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 23:56:57,049 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:57:02,112 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:57:07,270 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:57:07,598 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:57:08,146 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:57:08,567 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:57:08,878 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:57:13,966 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:57:14,688 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 6 rows
2024-07-13 23:57:19,770 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:57:24,933 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:57:30,060 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:57:35,199 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:57:40,331 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:57:46,945 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 76 rows
2024-07-13 23:57:51,275 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 48 rows
2024-07-13 23:57:56,380 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:58:01,456 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:58:13,209 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 138 rows
2024-07-13 23:58:18,265 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:58:20,846 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 19 rows
2024-07-13 23:58:21,156 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:58:21,469 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:58:21,772 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:58:26,855 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:58:32,105 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:58:35,302 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:58:35,616 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:58:36,215 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:58:41,332 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:58:46,433 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:58:46,754 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:58:47,405 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 6 rows
2024-07-13 23:58:47,710 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:58:48,616 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 5 rows
2024-07-13 23:58:53,708 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:58:58,867 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:59:06,002 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 64 rows
2024-07-13 23:59:11,198 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:59:22,932 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 137 rows
2024-07-13 23:59:27,907 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 59 rows
2024-07-13 23:59:29,076 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 4 rows
2024-07-13 23:59:29,694 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:59:39,259 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:59:41,254 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 21 rows
2024-07-13 23:59:42,813 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:59:45,568 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:59:50,673 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:59:51,974 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:59:53,567 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
2024-07-13 23:59:58,787 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_qingxi with 2 rows
