import os

folder_path = '/home/<USER>/Desktop/yxxbk_split_txt'
# except_filenames = '/home/<USER>/Desktop/except.txt'

except_filenames = set()

with open('/home/<USER>/Desktop/except.txt', 'r', encoding='utf-8') as f:
    for line in f:
        except_filenames.add(line.strip())


print(len(except_filenames))

for filename in os.listdir(folder_path):
    # 检查文件是否为 txt 文件
    if filename.endswith(".txt"):
        if str(filename)  not in except_filenames:
            continue  # 如果在 except.txt 中，跳过该文件
        print(1)