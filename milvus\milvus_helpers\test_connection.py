#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Milvus 连接测试工具
用于诊断和测试 Milvus 向量数据库的连接问题
"""

import socket
import time
import sys
import os

# 尝试导入 pymilvus，如果失败提供安装指导
try:
    from pymilvus import connections, utility
except ImportError:
    print("❌ 错误: 未找到 pymilvus 模块")
    print("💡 解决方案:")
    print("   1. 激活虚拟环境: 根据错误信息，可能需要激活 .venv 环境")
    print("   2. 安装 pymilvus: pip install pymilvus")
    print("   3. 或使用完整路径运行 Python")
    sys.exit(1)

try:
    from config import MILVUS_HOST, MILVUS_PORT
except ImportError:
    print("❌ 错误: 无法导入配置文件")
    print("💡 请确保 config.py 文件存在且可访问")
    sys.exit(1)

def test_network_connection(host, port, timeout=5):
    """测试网络连接"""
    print(f"🔍 正在测试网络连接: {host}:{port}")
    try:
        sock = socket.create_connection((host, port), timeout)
        sock.close()
        print(f"✅ 网络连接正常: {host}:{port}")
        return True
    except Exception as e:
        print(f"❌ 网络连接失败: {host}:{port} - {e}")
        return False

def test_milvus_service(host, port):
    """测试 Milvus 服务"""
    print(f"🔍 正在测试 Milvus 服务: {host}:{port}")
    try:
        # 尝试连接到 Milvus
        connections.connect(
            alias="test",
            host=host, 
            port=port,
            timeout=10
        )
        
        # 测试基本操作
        collections = utility.list_collections()
        print(f"✅ Milvus 服务正常，可用集合数量: {len(collections)}")
        print(f"📋 集合列表: {collections}")
        
        # 断开连接
        connections.disconnect("test")
        return True
        
    except Exception as e:
        print(f"❌ Milvus 服务连接失败: {e}")
        return False

def diagnose_connection():
    """全面诊断连接问题"""
    print("=" * 60)
    print("🔧 Milvus 连接诊断工具")
    print("=" * 60)
    
    print(f"📊 当前配置:")
    print(f"   主机: {MILVUS_HOST}")
    print(f"   端口: {MILVUS_PORT}")
    print()
    
    # 1. 测试网络连通性
    network_ok = test_network_connection(MILVUS_HOST, MILVUS_PORT)
    print()
    
    # 2. 测试 Milvus 服务
    if network_ok:
        service_ok = test_milvus_service(MILVUS_HOST, MILVUS_PORT)
    else:
        service_ok = False
        print("⏭️  跳过 Milvus 服务测试（网络连接失败）")
    
    print()
    print("=" * 60)
    print("📋 诊断结果:")
    print(f"   网络连接: {'✅ 正常' if network_ok else '❌ 失败'}")
    print(f"   Milvus 服务: {'✅ 正常' if service_ok else '❌ 失败'}")
    
    # 3. 提供解决建议
    print("\n🛠️  解决建议:")
    
    if not network_ok:
        print("   1. 检查网络连接和防火墙设置")
        print("   2. 确认主机地址和端口是否正确")
        print("   3. 联系系统管理员检查服务器状态")
    
    elif not service_ok:
        print("   1. 检查 Milvus 服务是否已启动")
        print("   2. 查看 Milvus 服务日志")
        print("   3. 确认 Milvus 配置文件设置")
        print("   4. 尝试重启 Milvus 服务")
    
    else:
        print("   ✨ 连接正常！如果仍有问题，可能是代码层面的问题。")
    
    print("=" * 60)
    
    return network_ok and service_ok

def test_alternative_hosts():
    """测试备选主机"""
    print("\n🔄 测试备选主机配置...")
    
    # 从配置文件中提取的备选主机
    alternative_hosts = [
        ("127.0.0.1", 19530),  # 本地服务
        ("localhost", 19530),   # 本地服务
        ("************", 19530), # 配置文件中注释的地址
        ("**************", 19530), # 配置文件中注释的测试地址
    ]
    
    working_hosts = []
    
    for host, port in alternative_hosts:
        print(f"\n测试主机: {host}:{port}")
        if test_network_connection(host, port, timeout=3):
            if test_milvus_service(host, port):
                working_hosts.append((host, port))
    
    if working_hosts:
        print(f"\n✅ 发现可用的主机:")
        for host, port in working_hosts:
            print(f"   - {host}:{port}")
        print("\n💡 建议: 更新配置文件中的 MILVUS_HOST 为上述可用主机之一")
    else:
        print("\n❌ 没有发现可用的备选主机")
    
    return working_hosts

if __name__ == "__main__":
    try:
        # 主要诊断
        main_ok = diagnose_connection()
        
        # 如果主要连接失败，测试备选方案
        if not main_ok:
            working_hosts = test_alternative_hosts()
            
            if working_hosts:
                print(f"\n🔧 快速修复建议:")
                best_host, best_port = working_hosts[0]
                print(f"   1. 修改 config.py 中的 MILVUS_HOST 为: '{best_host}'")
                print(f"   2. 或设置环境变量: set MILVUS_HOST={best_host}")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 诊断过程中发生错误: {e}")
