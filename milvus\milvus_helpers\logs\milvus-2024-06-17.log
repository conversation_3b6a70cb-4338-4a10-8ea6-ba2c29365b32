2024-06-17 17:20:14,567 ｜ INFO ｜ milvus_helpers.py ｜ __init__ ｜ 14 ｜ Successfully connect to Milvus with IP:************* and PORT:19530
2024-06-17 17:20:16,306 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:20:17,737 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 17:20:27,007 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 120 rows
2024-06-17 17:20:36,307 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:20:51,214 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 87 rows
2024-06-17 17:20:53,126 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 17:20:55,661 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 26 rows
2024-06-17 17:20:58,420 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 31 rows
2024-06-17 17:21:00,026 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 17:21:04,831 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 17:21:07,768 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 59 rows
2024-06-17 17:21:08,550 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 17:21:09,924 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 17:21:15,707 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 29 rows
2024-06-17 17:21:16,661 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 17:21:17,598 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 17:21:26,676 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 79 rows
2024-06-17 17:21:27,410 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 17:21:28,738 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 17:21:34,827 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:21:37,496 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 17:21:50,482 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 74 rows
2024-06-17 17:21:52,210 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 17:21:53,106 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:21:53,787 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 17:21:54,097 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:21:54,785 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 17:21:56,117 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:21:57,427 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 17:21:59,514 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 38 rows
2024-06-17 17:22:00,130 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 17:22:03,262 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 17:22:09,040 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:22:10,477 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 32 rows
2024-06-17 17:22:16,157 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 103 rows
2024-06-17 17:22:17,799 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 18 rows
2024-06-17 17:22:19,429 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 17:22:20,753 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 19 rows
2024-06-17 17:22:21,318 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 17:22:23,500 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 17:22:26,159 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 17:22:26,433 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:22:28,368 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:22:31,079 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 17:22:33,546 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 39 rows
2024-06-17 17:22:34,682 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 17:22:43,545 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 17:22:45,019 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 19 rows
2024-06-17 17:22:47,986 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 53 rows
2024-06-17 17:22:48,810 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 17:22:50,463 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:22:50,966 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:23:01,957 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 29 rows
2024-06-17 17:23:03,853 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 17:23:05,535 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 17:23:07,202 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 17:23:09,038 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 17:23:10,430 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 17:23:12,798 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 17:23:14,967 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 25 rows
2024-06-17 17:23:16,166 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 17:23:16,905 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 17:23:21,934 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 78 rows
2024-06-17 17:23:28,056 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 105 rows
2024-06-17 17:23:28,829 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 17:23:29,669 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 17:23:36,188 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 84 rows
2024-06-17 17:23:40,692 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:23:41,316 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:23:42,002 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:23:43,567 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 17:23:45,644 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 17:23:59,424 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 28 rows
2024-06-17 17:24:01,202 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 27 rows
2024-06-17 17:24:02,361 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:24:04,134 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 17:24:05,083 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 17:24:05,429 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:24:09,130 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 32 rows
2024-06-17 17:24:14,605 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 57 rows
2024-06-17 17:24:15,445 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 17:24:21,536 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 78 rows
2024-06-17 17:24:24,691 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 17:24:25,666 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:24:33,460 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:24:34,717 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:24:35,785 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 18 rows
2024-06-17 17:24:41,454 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 17:24:42,495 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 17:24:49,493 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 17:24:50,387 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 17:24:51,177 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:24:53,653 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 29 rows
2024-06-17 17:24:54,035 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 17:25:00,915 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 17:25:01,786 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 17:25:03,585 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 31 rows
2024-06-17 17:25:05,878 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 24 rows
2024-06-17 17:25:06,821 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 17:25:08,736 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 17:25:16,134 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 17:25:20,363 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 17:25:27,265 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 19 rows
2024-06-17 17:25:28,410 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 17:25:34,230 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 17:25:35,292 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 17:25:48,332 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 17:25:49,237 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 17:25:49,843 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:25:51,103 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:25:53,974 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:25:54,464 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:26:06,050 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 64 rows
2024-06-17 17:26:08,013 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 17:26:10,659 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 28 rows
2024-06-17 17:26:11,217 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:26:13,225 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:26:13,712 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 17:26:17,809 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 60 rows
2024-06-17 17:26:19,679 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 31 rows
2024-06-17 17:26:21,241 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 34 rows
2024-06-17 17:26:24,663 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 17:26:30,857 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:26:32,290 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 17:26:33,459 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:26:35,104 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:26:36,222 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 19 rows
2024-06-17 17:26:37,785 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 17:26:40,474 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 17:26:40,803 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:26:41,596 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:26:46,428 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 19 rows
2024-06-17 17:26:49,011 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 17:26:54,168 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 17:26:55,365 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 17:26:58,425 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 17:27:00,376 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 17:27:00,842 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 17:27:02,752 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 17:27:07,164 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 85 rows
2024-06-17 17:27:11,975 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 17:27:17,974 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 17:27:21,272 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 17:27:22,296 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 17:27:24,050 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 24 rows
2024-06-17 17:27:24,485 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:27:26,049 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 17:27:28,004 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:27:28,587 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:27:30,531 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 17:27:31,706 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 17:27:34,003 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 17:27:38,052 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:27:38,421 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:27:43,368 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 74 rows
2024-06-17 17:27:45,353 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 37 rows
2024-06-17 17:27:45,938 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 17:27:48,416 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 17:27:55,045 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 111 rows
2024-06-17 17:27:57,200 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 17:27:59,151 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 24 rows
2024-06-17 17:28:01,443 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 17:28:03,467 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:28:06,373 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1 rows
2024-06-17 17:28:08,149 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:28:13,357 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 102 rows
2024-06-17 17:28:13,731 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 17:28:14,577 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 17:28:15,888 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 31 rows
2024-06-17 17:28:17,506 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 17:28:19,474 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 17:28:26,683 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 17:28:27,705 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-17 17:28:43,592 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 91 rows
2024-06-17 17:28:44,119 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 17:28:46,683 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 17:28:54,628 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 17:28:56,292 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 29 rows
2024-06-17 17:28:59,867 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:29:03,682 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 39 rows
2024-06-17 17:29:06,297 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 34 rows
2024-06-17 17:29:09,888 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 17:29:10,346 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:29:13,922 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 26 rows
2024-06-17 17:29:15,351 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 17:29:16,283 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:29:18,174 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 17:29:18,906 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 17:29:20,902 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 17:29:22,702 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 17:29:23,128 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:29:23,512 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 17:29:27,554 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 17:29:28,090 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:29:28,565 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:29:30,468 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 17:29:32,691 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 17:29:34,293 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:29:34,896 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:29:37,113 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 17:29:39,230 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 41 rows
2024-06-17 17:29:39,621 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 17:29:49,000 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 173 rows
2024-06-17 17:29:52,154 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 17:29:52,504 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:29:54,254 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 29 rows
2024-06-17 17:29:56,437 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:29:57,156 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 17:30:00,441 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 17:30:01,227 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:30:01,591 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:30:02,074 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:30:03,179 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 17:30:06,030 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 17:30:06,313 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1 rows
2024-06-17 17:30:11,645 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 41 rows
2024-06-17 17:30:13,547 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:30:14,922 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:30:16,885 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 17:30:17,595 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 17:30:23,624 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 98 rows
2024-06-17 17:30:24,262 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:30:25,442 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:30:26,963 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-17 17:30:40,986 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:30:46,897 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:30:51,340 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:30:52,243 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 17:30:53,676 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 17:30:55,744 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 38 rows
2024-06-17 17:30:56,561 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 17:30:57,636 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:30:58,287 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 17:30:59,237 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:31:00,887 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:31:01,199 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:31:02,648 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 28 rows
2024-06-17 17:31:03,645 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:31:04,175 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:31:05,222 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 17:31:11,646 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 105 rows
2024-06-17 17:31:20,622 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 34 rows
2024-06-17 17:31:21,603 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 17:31:22,258 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:31:22,941 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 17:31:23,274 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:31:23,981 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 17:31:24,543 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:31:29,146 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 53 rows
2024-06-17 17:31:31,405 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 41 rows
2024-06-17 17:31:33,248 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 17:31:34,155 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 17:31:41,708 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 17:31:42,331 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:31:42,701 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 17:31:46,315 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 44 rows
2024-06-17 17:31:50,216 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 68 rows
2024-06-17 17:31:54,852 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 90 rows
2024-06-17 17:32:07,816 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 24 rows
2024-06-17 17:32:09,156 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 18 rows
2024-06-17 17:32:10,830 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 17:32:16,393 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 17:32:16,785 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:32:22,111 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 48 rows
2024-06-17 17:32:23,827 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:32:25,210 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 17:32:25,735 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:32:27,085 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 17:32:28,473 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 17:32:29,966 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 19 rows
2024-06-17 17:32:35,361 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 53 rows
2024-06-17 17:32:35,921 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:32:50,330 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 28 rows
2024-06-17 17:32:51,261 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 17:32:51,575 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:32:52,883 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 18 rows
2024-06-17 17:32:53,339 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 17:32:55,644 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 17:32:57,763 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 32 rows
2024-06-17 17:33:05,671 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:33:10,610 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 26 rows
2024-06-17 17:33:16,798 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:33:18,626 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 17:33:20,443 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 17:33:21,622 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:33:24,684 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 17:33:32,698 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:33:34,795 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 17:33:35,893 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 17:33:37,225 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 17:33:53,982 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 17:33:58,111 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 17:33:58,737 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 17:33:59,133 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 17:34:05,337 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 27 rows
2024-06-17 17:34:06,322 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:34:06,629 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:34:07,095 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1 rows
2024-06-17 17:34:09,987 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 18 rows
2024-06-17 17:34:12,069 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 29 rows
2024-06-17 17:34:16,399 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-17 17:34:16,764 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 17:34:18,624 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 17:34:19,077 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:34:19,643 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 17:34:19,953 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:34:20,780 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 17:34:28,989 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 94 rows
2024-06-17 17:34:29,907 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 17:34:31,724 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 17:34:33,927 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 17:34:39,572 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 112 rows
2024-06-17 17:34:40,629 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 17:34:41,610 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 17:34:43,748 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 17:34:45,824 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:34:47,540 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 17:34:52,733 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 41 rows
2024-06-17 17:34:53,610 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:34:59,267 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 82 rows
2024-06-17 17:35:00,374 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 18 rows
2024-06-17 17:35:01,424 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 18 rows
2024-06-17 17:35:03,633 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 32 rows
2024-06-17 17:35:11,076 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 34 rows
2024-06-17 17:35:13,042 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 17:35:13,418 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:35:13,734 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:35:16,958 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 19 rows
2024-06-17 17:35:18,517 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 17:35:20,331 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 17:35:21,930 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 17:35:23,222 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 17:35:24,315 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 19 rows
2024-06-17 17:35:28,118 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 40 rows
2024-06-17 17:35:31,898 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 92 rows
2024-06-17 17:35:34,096 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 17:35:34,699 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 17:35:37,809 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 17:35:38,697 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:35:40,085 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:35:41,759 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 29 rows
2024-06-17 17:35:46,231 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 49 rows
2024-06-17 17:35:48,006 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:35:51,484 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 17:35:53,827 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 41 rows
2024-06-17 17:35:56,385 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 17:35:58,605 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 18 rows
2024-06-17 17:35:59,736 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 17:36:00,462 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:36:04,499 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 17:36:06,451 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 17:36:10,127 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 57 rows
2024-06-17 17:36:11,257 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 18 rows
2024-06-17 17:36:11,612 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:36:17,669 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 148 rows
2024-06-17 17:36:25,004 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 17:36:25,338 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:36:26,310 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:36:31,095 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:36:32,515 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 17:36:35,456 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 17:36:36,720 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:36:37,778 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 17:36:38,896 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 17:36:45,749 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:36:47,800 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 37 rows
2024-06-17 17:36:50,982 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 64 rows
2024-06-17 17:36:51,803 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 17:36:53,440 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 17:36:54,653 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 17:36:55,662 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 17:36:56,058 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:36:57,526 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 17:36:59,368 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 17:37:00,681 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 17:37:01,492 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 17:37:03,759 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:37:05,207 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 31 rows
2024-06-17 17:37:06,935 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 17:37:09,399 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 34 rows
2024-06-17 17:37:10,356 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 17:37:10,983 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:37:14,382 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 17:37:15,951 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:37:18,370 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 41 rows
2024-06-17 17:37:25,491 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:37:26,587 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 17:37:27,583 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 17:37:29,088 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 17:37:33,625 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 17:37:35,013 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 19 rows
2024-06-17 17:37:36,573 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 24 rows
2024-06-17 17:37:43,433 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 17:37:47,172 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 33 rows
2024-06-17 17:37:52,761 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 17:37:54,931 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 25 rows
2024-06-17 17:37:55,606 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 17:37:57,579 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 17:37:57,887 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:38:02,275 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 17:38:07,696 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 111 rows
2024-06-17 17:38:09,169 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 18 rows
2024-06-17 17:38:09,469 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:38:09,826 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:38:11,645 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 17:38:12,713 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-17 17:38:14,331 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 17:38:16,692 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 41 rows
2024-06-17 17:38:18,257 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:38:19,046 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 17:38:19,982 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 17:38:20,621 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 17:38:28,901 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:38:29,426 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:38:30,580 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:38:33,060 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 17:38:39,009 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 17:38:41,972 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 37 rows
2024-06-17 17:38:42,668 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:38:52,403 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 17:38:52,747 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:38:57,025 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 17:39:00,975 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 17:39:10,683 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 17:39:12,805 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 41 rows
2024-06-17 17:39:18,028 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 74 rows
2024-06-17 17:39:20,979 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:39:21,622 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:39:22,247 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:39:22,648 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:39:24,938 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 17:39:28,703 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 17:39:29,630 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 17:39:30,009 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:39:37,991 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 17:39:46,361 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 17:39:46,763 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:39:52,082 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 17:39:54,355 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 17:39:55,074 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 17:39:57,082 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 24 rows
2024-06-17 17:39:57,420 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:39:57,931 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:39:59,136 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 17:40:08,862 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 69 rows
2024-06-17 17:40:09,438 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 17:40:09,770 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:40:16,838 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:40:30,598 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:40:31,242 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 17:40:31,563 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:40:32,352 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:40:35,264 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:40:37,066 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 26 rows
2024-06-17 17:40:42,287 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 17:40:43,369 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:40:44,687 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 17:40:45,280 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:40:51,013 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 129 rows
2024-06-17 17:40:53,656 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 41 rows
2024-06-17 17:41:03,451 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:41:05,523 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:41:06,913 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:41:07,224 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:41:07,793 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:41:08,590 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 17:41:12,436 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 44 rows
2024-06-17 17:41:13,922 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 17:41:14,477 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:41:15,483 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:41:21,154 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 17:41:22,464 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-17 17:41:24,106 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 17:41:29,089 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:41:33,715 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 17:41:34,726 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 17:41:37,700 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 17:41:38,518 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 17:41:40,135 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 17:41:41,163 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 17:41:43,731 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 17:41:46,988 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:41:49,145 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 17:41:49,759 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 17:41:51,360 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 17:41:51,674 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 17:41:54,039 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 33 rows
2024-06-17 17:41:57,174 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 17:41:57,995 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 17:41:58,800 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 17:42:01,773 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 31 rows
2024-06-17 17:42:02,066 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:42:02,471 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:42:03,963 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 17:42:04,424 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:42:06,394 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 17:42:08,207 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:42:09,011 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 17:42:10,981 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 41 rows
2024-06-17 17:42:12,735 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 27 rows
2024-06-17 17:42:20,493 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 89 rows
2024-06-17 17:42:23,478 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 17:42:24,414 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 17:42:28,519 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:42:31,212 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 17:42:31,882 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 17:42:32,230 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 17:42:34,946 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 19 rows
2024-06-17 17:42:35,493 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 17:42:38,837 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 19 rows
2024-06-17 17:42:40,242 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-17 17:42:40,705 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:42:41,238 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 17:42:41,542 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:42:50,359 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 17:42:51,394 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 17:42:51,850 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 17:42:52,721 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 17:42:57,694 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 18:54:53,513 ｜ INFO ｜ milvus_helpers.py ｜ __init__ ｜ 14 ｜ Successfully connect to Milvus with IP:************* and PORT:19530
2024-06-17 18:54:54,734 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 18:54:56,383 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 27 rows
2024-06-17 18:54:58,370 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 18:54:59,005 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 18:54:59,821 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 18:55:00,538 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-17 18:55:09,817 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 136 rows
2024-06-17 18:55:24,750 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 233 rows
2024-06-17 18:55:26,153 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 18:55:35,278 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 136 rows
2024-06-17 18:55:38,998 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 52 rows
2024-06-17 18:55:40,055 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 18:55:50,671 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 145 rows
2024-06-17 18:55:58,704 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 126 rows
2024-06-17 18:56:06,801 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 110 rows
2024-06-17 18:56:08,544 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 26 rows
2024-06-17 18:56:15,142 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 90 rows
2024-06-17 18:56:17,824 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 18:56:19,555 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 18:56:19,900 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 18:56:20,550 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 18:56:26,509 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 136 rows
2024-06-17 18:56:27,247 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 18:56:28,112 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 18:56:30,178 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 41 rows
2024-06-17 18:56:35,002 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 99 rows
2024-06-17 18:56:35,897 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 18:56:45,478 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 83 rows
2024-06-17 18:56:47,028 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 18:56:57,368 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 131 rows
2024-06-17 18:56:58,137 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 18:56:58,488 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 18:57:00,892 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 18:57:03,896 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 18:57:09,529 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 60 rows
2024-06-17 18:57:16,517 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 113 rows
2024-06-17 18:57:19,821 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 18:57:22,790 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 65 rows
2024-06-17 18:57:25,140 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 53 rows
2024-06-17 18:57:27,536 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 19 rows
2024-06-17 18:57:30,665 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 58 rows
2024-06-17 18:57:32,860 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 43 rows
2024-06-17 18:57:55,227 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 370 rows
2024-06-17 18:57:55,700 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 18:57:56,266 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 18:57:57,536 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 18:57:59,401 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 18:58:00,848 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 18:58:06,160 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 32 rows
2024-06-17 18:58:07,953 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 18:58:08,932 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 18:58:22,110 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 18:58:22,707 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 18:58:23,787 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 18:58:24,977 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 18:58:25,452 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 18:58:26,375 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 18:58:27,591 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 18:58:28,797 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 18:58:30,677 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 33 rows
2024-06-17 18:58:39,032 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 151 rows
2024-06-17 18:58:42,955 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 48 rows
2024-06-17 18:58:56,669 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 221 rows
2024-06-17 18:58:57,478 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 18:59:06,999 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 89 rows
2024-06-17 18:59:07,812 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 18:59:15,858 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 131 rows
2024-06-17 18:59:16,405 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 18:59:17,317 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 18:59:20,476 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 53 rows
2024-06-17 18:59:22,729 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 18:59:30,136 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 164 rows
2024-06-17 18:59:32,129 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 18:59:33,238 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 18:59:34,293 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 18:59:37,064 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 34 rows
2024-06-17 18:59:37,832 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 18:59:43,149 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 47 rows
2024-06-17 18:59:43,790 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 18:59:44,566 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 18:59:46,945 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 28 rows
2024-06-17 18:59:49,367 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 18:59:50,562 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 18:59:57,713 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 133 rows
2024-06-17 18:59:58,565 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 18:59:59,508 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:00:00,088 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:00:02,245 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 19:00:02,853 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:00:06,931 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 41 rows
2024-06-17 19:00:11,947 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 58 rows
2024-06-17 19:00:14,842 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 39 rows
2024-06-17 19:00:21,943 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 154 rows
2024-06-17 19:00:22,960 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 19:00:23,330 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 19:00:24,097 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:00:27,166 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 70 rows
2024-06-17 19:00:28,673 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 33 rows
2024-06-17 19:00:29,170 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:00:31,379 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 54 rows
2024-06-17 19:00:35,016 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 55 rows
2024-06-17 19:00:36,722 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 19:00:44,908 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 141 rows
2024-06-17 19:01:17,065 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 479 rows
2024-06-17 19:01:21,390 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 81 rows
2024-06-17 19:01:27,158 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 118 rows
2024-06-17 19:01:28,167 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 19 rows
2024-06-17 19:01:34,205 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 125 rows
2024-06-17 19:01:44,229 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 131 rows
2024-06-17 19:01:47,086 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 44 rows
2024-06-17 19:01:47,848 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:01:48,262 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:01:50,802 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 19:01:51,462 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:01:52,614 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 19:01:57,365 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 122 rows
2024-06-17 19:01:58,085 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 19:02:00,186 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 39 rows
2024-06-17 19:02:04,639 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:02:07,057 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 53 rows
2024-06-17 19:02:08,661 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 31 rows
2024-06-17 19:02:16,697 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 134 rows
2024-06-17 19:02:17,971 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:02:20,703 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 26 rows
2024-06-17 19:02:35,141 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-17 19:02:35,573 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:02:43,030 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:02:43,403 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:02:43,799 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:02:48,970 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 110 rows
2024-06-17 19:02:49,703 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 19:02:50,261 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:02:50,908 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 19:02:55,975 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 129 rows
2024-06-17 19:02:56,625 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 19:03:19,572 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 341 rows
2024-06-17 19:03:20,071 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:03:20,951 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:03:22,083 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 19:03:26,241 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 94 rows
2024-06-17 19:03:28,490 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 51 rows
2024-06-17 19:03:30,838 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 53 rows
2024-06-17 19:03:33,260 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 19:03:39,634 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 88 rows
2024-06-17 19:03:41,778 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:03:43,620 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:03:46,548 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 64 rows
2024-06-17 19:03:55,305 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 75 rows
2024-06-17 19:04:05,625 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 66 rows
2024-06-17 19:04:07,035 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:04:19,066 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-17 19:04:21,704 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 19:04:24,144 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 19:04:29,030 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 112 rows
2024-06-17 19:04:44,562 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:04:51,602 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 19:04:52,179 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 19:04:54,329 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 19:04:54,817 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:05:04,743 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 19:05:08,581 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 44 rows
2024-06-17 19:05:08,960 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:05:15,007 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 129 rows
2024-06-17 19:05:20,339 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 69 rows
2024-06-17 19:05:22,215 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 41 rows
2024-06-17 19:05:28,348 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 133 rows
2024-06-17 19:05:29,172 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 19:05:30,970 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 44 rows
2024-06-17 19:05:32,660 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 19:05:44,492 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 169 rows
2024-06-17 19:05:49,855 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 93 rows
2024-06-17 19:05:51,881 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 34 rows
2024-06-17 19:05:52,252 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:05:52,960 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-17 19:05:53,734 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 19:05:56,921 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 78 rows
2024-06-17 19:05:58,627 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 19:06:02,395 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:06:09,023 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 92 rows
2024-06-17 19:06:11,067 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 43 rows
2024-06-17 19:06:13,522 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 54 rows
2024-06-17 19:06:15,641 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 28 rows
2024-06-17 19:06:22,039 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 32 rows
2024-06-17 19:06:22,804 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:06:41,793 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 70 rows
2024-06-17 19:06:42,970 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:06:44,824 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:06:45,304 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:06:45,879 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:06:47,635 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:06:48,386 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:06:54,293 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 48 rows
2024-06-17 19:06:54,789 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:06:55,130 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 19:07:06,532 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 19:07:08,761 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 48 rows
2024-06-17 19:07:11,367 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 29 rows
2024-06-17 19:07:12,913 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:07:14,035 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:07:16,000 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 39 rows
2024-06-17 19:07:18,230 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 19:07:19,315 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:07:20,711 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:07:26,210 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 131 rows
2024-06-17 19:07:26,758 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:07:28,220 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 28 rows
2024-06-17 19:07:29,969 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 29 rows
2024-06-17 19:07:32,849 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 45 rows
2024-06-17 19:07:33,651 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:07:41,117 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 88 rows
2024-06-17 19:07:43,227 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 19:07:47,572 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 74 rows
2024-06-17 19:07:48,490 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:07:50,552 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 33 rows
2024-06-17 19:07:57,431 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 131 rows
2024-06-17 19:07:58,221 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:07:59,115 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 19:08:29,274 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 494 rows
2024-06-17 19:08:31,324 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 33 rows
2024-06-17 19:08:32,533 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:08:58,175 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 327 rows
2024-06-17 19:08:59,100 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:09:05,926 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 48 rows
2024-06-17 19:09:07,479 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:09:10,424 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 32 rows
2024-06-17 19:09:11,671 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:09:13,769 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 19:09:21,776 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 109 rows
2024-06-17 19:09:26,333 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 112 rows
2024-06-17 19:09:27,133 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-17 19:09:27,980 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 19:09:29,374 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 19:09:31,130 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 27 rows
2024-06-17 19:09:33,792 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 19:09:40,702 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 120 rows
2024-06-17 19:09:45,027 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 42 rows
2024-06-17 19:09:50,128 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 86 rows
2024-06-17 19:09:57,591 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 131 rows
2024-06-17 19:09:57,968 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:10:00,498 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 52 rows
2024-06-17 19:10:01,074 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:10:02,134 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 19:10:03,088 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 19:10:05,571 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 39 rows
2024-06-17 19:10:05,885 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:10:11,039 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 94 rows
2024-06-17 19:10:13,102 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 19:10:14,472 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:10:17,153 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 19:10:20,942 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 77 rows
2024-06-17 19:10:21,567 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 19:10:38,862 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 274 rows
2024-06-17 19:10:39,563 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:10:42,867 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 27 rows
2024-06-17 19:10:44,569 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 19:10:46,838 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 34 rows
2024-06-17 19:10:49,785 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 44 rows
2024-06-17 19:10:53,330 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 80 rows
2024-06-17 19:10:54,105 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 19:10:56,461 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:10:57,154 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 19:10:58,050 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 19:10:58,875 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:11:02,189 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 80 rows
2024-06-17 19:11:03,825 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 19:11:13,118 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 130 rows
2024-06-17 19:11:14,337 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:11:18,215 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 59 rows
2024-06-17 19:11:18,753 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:11:20,810 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 19:11:23,607 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 46 rows
2024-06-17 19:11:23,980 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:11:25,409 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 32 rows
2024-06-17 19:11:32,760 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 133 rows
2024-06-17 19:11:43,952 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 153 rows
2024-06-17 19:11:44,614 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 19:11:51,392 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 134 rows
2024-06-17 19:11:52,797 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 19:11:56,783 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 83 rows
2024-06-17 19:11:57,534 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 19:11:58,362 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:12:01,602 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 19:12:02,426 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:12:07,061 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 53 rows
2024-06-17 19:12:15,393 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:12:19,846 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 74 rows
2024-06-17 19:12:20,208 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 19:12:22,874 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 19:12:23,790 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 19:12:24,990 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 19:12:29,889 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 47 rows
2024-06-17 19:12:32,424 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 42 rows
2024-06-17 19:12:33,299 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:12:35,123 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:12:45,460 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 151 rows
2024-06-17 19:12:54,168 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 123 rows
2024-06-17 19:12:54,817 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 19:12:55,656 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 19:12:56,910 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 19:12:59,379 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 57 rows
2024-06-17 19:13:00,252 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:13:08,411 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 122 rows
2024-06-17 19:13:10,427 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:13:11,665 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 19:13:13,154 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:13:14,921 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:13:18,593 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 85 rows
2024-06-17 19:13:22,049 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 40 rows
2024-06-17 19:13:26,553 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 91 rows
2024-06-17 19:13:27,609 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 19:13:29,255 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 31 rows
2024-06-17 19:13:29,698 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:13:29,996 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:13:30,861 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-17 19:13:44,134 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 225 rows
2024-06-17 19:13:46,018 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 39 rows
2024-06-17 19:13:46,482 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:13:49,533 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 49 rows
2024-06-17 19:13:52,561 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 31 rows
2024-06-17 19:13:53,453 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 19:14:17,862 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 341 rows
2024-06-17 19:14:18,926 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:14:26,956 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 118 rows
2024-06-17 19:14:29,892 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 39 rows
2024-06-17 19:14:31,380 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 19 rows
2024-06-17 19:14:31,904 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:14:34,640 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 27 rows
2024-06-17 19:14:35,215 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 19:14:36,529 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:14:37,686 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 19:14:39,696 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 19:14:45,247 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 105 rows
2024-06-17 19:14:50,098 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 100 rows
2024-06-17 19:14:51,064 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:14:53,384 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 19:14:57,567 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 91 rows
2024-06-17 19:14:58,186 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:15:00,695 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:15:01,999 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 19:15:03,145 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:15:07,307 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:15:09,288 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 38 rows
2024-06-17 19:15:13,913 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 19:15:14,488 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 19:15:19,395 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 115 rows
2024-06-17 19:15:20,086 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:15:21,118 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 19 rows
2024-06-17 19:15:26,228 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 112 rows
2024-06-17 19:15:27,122 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 18 rows
2024-06-17 19:15:29,698 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 52 rows
2024-06-17 19:15:34,451 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 78 rows
2024-06-17 19:15:40,271 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 28 rows
2024-06-17 19:15:43,220 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 43 rows
2024-06-17 19:15:43,600 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:15:44,898 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 19:15:46,896 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:15:53,545 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 83 rows
2024-06-17 19:15:54,888 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:15:55,402 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:15:58,274 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 58 rows
2024-06-17 19:15:59,490 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 19:16:02,323 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 34 rows
2024-06-17 19:16:04,035 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 19:16:05,078 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 19:16:05,493 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:16:07,419 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:16:08,375 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:16:10,788 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 19:16:11,229 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:16:11,717 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:16:13,309 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 19:16:15,060 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 28 rows
2024-06-17 19:16:16,241 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 19:16:19,576 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 19:16:20,314 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:16:21,048 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:16:23,054 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 47 rows
2024-06-17 19:16:27,704 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 59 rows
2024-06-17 19:16:28,531 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 19:16:29,687 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 19:16:45,721 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 287 rows
2024-06-17 19:16:49,835 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 47 rows
2024-06-17 19:16:54,523 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 19:16:54,928 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:16:55,801 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:16:56,545 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 19:16:57,865 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 19:17:02,262 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 90 rows
2024-06-17 19:17:06,359 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 44 rows
2024-06-17 19:17:07,604 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 19:17:15,171 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 97 rows
2024-06-17 19:17:16,525 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:17:27,471 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 229 rows
2024-06-17 19:17:27,824 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:17:30,521 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 34 rows
2024-06-17 19:17:31,890 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:17:43,336 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 114 rows
2024-06-17 19:17:44,672 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 19:17:46,554 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 24 rows
2024-06-17 19:17:47,488 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:17:48,521 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 19 rows
2024-06-17 19:17:55,197 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 120 rows
2024-06-17 19:17:56,037 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 19:17:57,106 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:18:14,173 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 287 rows
2024-06-17 19:18:16,145 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 19:18:18,501 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 19:18:19,958 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 19:18:21,397 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 24 rows
2024-06-17 19:18:23,378 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 19:18:24,290 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 19:18:27,580 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 56 rows
2024-06-17 19:18:27,886 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:18:32,373 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 97 rows
2024-06-17 19:18:35,224 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 39 rows
2024-06-17 19:18:35,985 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 19:18:37,610 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:18:40,550 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 19:18:42,399 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 29 rows
2024-06-17 19:18:43,688 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 19:18:44,235 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:18:46,375 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 28 rows
2024-06-17 19:18:47,217 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:18:48,515 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:18:49,242 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 19:18:57,550 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 113 rows
2024-06-17 19:18:58,307 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 19:18:58,779 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:19:00,940 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 26 rows
2024-06-17 19:19:10,183 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 154 rows
2024-06-17 19:19:10,788 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 19:19:22,606 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 230 rows
2024-06-17 19:19:24,141 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 19:19:28,196 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 105 rows
2024-06-17 19:19:36,779 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 164 rows
2024-06-17 19:19:38,247 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 19:19:45,174 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 110 rows
2024-06-17 19:19:47,541 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 19:19:50,595 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 25 rows
2024-06-17 19:20:11,777 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 355 rows
2024-06-17 19:20:13,529 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 19:20:15,099 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 19 rows
2024-06-17 19:20:16,005 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 19:20:25,267 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 133 rows
2024-06-17 19:20:26,193 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 19:20:28,586 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 54 rows
2024-06-17 19:20:29,324 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 19:20:30,867 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 19:20:36,725 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 93 rows
2024-06-17 19:20:37,869 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 24 rows
2024-06-17 19:20:39,823 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 31 rows
2024-06-17 19:20:40,395 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:20:54,280 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 254 rows
2024-06-17 19:20:54,631 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 19:21:01,826 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 67 rows
2024-06-17 19:21:02,874 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:21:04,068 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:21:16,759 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 126 rows
2024-06-17 19:21:19,108 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 56 rows
2024-06-17 19:21:20,803 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:21:21,618 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:21:23,896 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 19:21:29,114 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 118 rows
2024-06-17 19:21:29,799 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:21:30,097 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:21:30,766 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 19:21:32,974 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 37 rows
2024-06-17 19:21:33,644 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 19:21:35,161 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 19:21:38,128 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 69 rows
2024-06-17 19:21:38,958 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 19:21:39,941 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:21:46,451 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 77 rows
2024-06-17 19:21:46,955 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:21:55,133 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 124 rows
2024-06-17 19:21:56,528 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 19:22:01,164 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 110 rows
2024-06-17 19:22:02,370 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:22:03,550 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:22:08,967 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 88 rows
2024-06-17 19:22:09,378 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:22:14,609 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 94 rows
2024-06-17 19:22:22,234 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 136 rows
2024-06-17 19:22:22,877 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 19:22:23,596 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 19:22:27,662 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 106 rows
2024-06-17 19:22:32,761 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 99 rows
2024-06-17 19:22:37,473 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 113 rows
2024-06-17 19:22:42,003 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 55 rows
2024-06-17 19:22:47,553 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 44 rows
2024-06-17 19:22:48,323 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:22:49,282 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:22:52,239 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 70 rows
2024-06-17 19:22:52,610 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:22:53,088 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 19:22:53,801 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 19:22:54,733 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 18 rows
2024-06-17 19:22:56,127 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 34 rows
2024-06-17 19:22:58,991 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 67 rows
2024-06-17 19:23:04,261 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 58 rows
2024-06-17 19:23:08,262 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 82 rows
2024-06-17 19:23:09,068 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:23:10,610 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 19:23:15,804 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 55 rows
2024-06-17 19:23:37,859 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 267 rows
2024-06-17 19:23:39,936 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 41 rows
2024-06-17 19:23:42,101 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 19:23:46,470 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 76 rows
2024-06-17 19:23:50,957 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 64 rows
2024-06-17 19:23:51,556 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 19:23:53,831 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 47 rows
2024-06-17 19:24:07,793 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 230 rows
2024-06-17 19:24:08,533 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 19:24:09,048 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 19:24:14,701 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 119 rows
2024-06-17 19:24:15,573 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 19:24:17,384 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 19:24:18,587 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 19:24:20,856 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:24:24,987 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 57 rows
2024-06-17 19:24:26,482 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 19:24:27,350 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 19:24:41,829 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 45 rows
2024-06-17 19:24:44,822 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 25 rows
2024-06-17 19:24:45,516 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:24:46,740 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 19:24:48,012 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 26 rows
2024-06-17 19:24:48,588 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 19:24:51,222 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 19:24:52,436 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 24 rows
2024-06-17 19:24:56,817 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 106 rows
2024-06-17 19:24:57,506 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:25:02,092 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 118 rows
2024-06-17 19:25:03,841 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 19:25:06,101 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 19:25:07,415 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 19:25:28,508 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 351 rows
2024-06-17 19:25:29,427 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 19:25:31,541 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 53 rows
2024-06-17 19:25:33,983 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 19:25:55,537 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 261 rows
2024-06-17 19:25:56,100 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 19:25:59,883 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 46 rows
2024-06-17 19:26:00,289 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:26:01,955 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 37 rows
2024-06-17 19:26:04,544 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 19:26:08,210 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 37 rows
2024-06-17 19:26:09,813 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 28 rows
2024-06-17 19:26:10,291 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:26:11,551 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 19:26:27,485 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 280 rows
2024-06-17 19:26:32,171 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 116 rows
2024-06-17 19:26:37,367 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 69 rows
2024-06-17 19:26:37,937 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 19:26:39,169 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 24 rows
2024-06-17 19:26:40,966 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 19:26:45,549 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 27 rows
2024-06-17 19:26:50,961 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 84 rows
2024-06-17 19:26:51,681 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 19:26:53,593 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 19:26:58,597 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 115 rows
2024-06-17 19:27:04,790 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 140 rows
2024-06-17 19:27:07,351 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 47 rows
2024-06-17 19:27:10,656 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 45 rows
2024-06-17 19:27:11,267 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:27:15,750 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 85 rows
2024-06-17 19:27:17,117 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:27:17,868 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:27:23,123 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 103 rows
2024-06-17 19:27:24,259 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 19:27:25,808 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 19:27:26,270 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:27:27,231 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 19 rows
2024-06-17 19:27:31,693 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 113 rows
2024-06-17 19:27:37,316 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 78 rows
2024-06-17 19:27:38,555 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 19:27:41,321 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 19 rows
2024-06-17 19:28:18,130 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 616 rows
2024-06-17 19:28:19,491 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 31 rows
2024-06-17 19:28:25,292 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 118 rows
2024-06-17 19:28:25,932 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:28:28,202 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 51 rows
2024-06-17 19:28:31,863 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 92 rows
2024-06-17 19:28:38,480 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 66 rows
2024-06-17 19:28:46,566 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 124 rows
2024-06-17 19:28:52,618 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 85 rows
2024-06-17 19:28:58,571 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 131 rows
2024-06-17 19:28:59,951 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 24 rows
2024-06-17 19:29:00,679 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 19:29:01,532 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-17 19:29:04,610 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 28 rows
2024-06-17 19:29:07,359 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 39 rows
2024-06-17 19:29:17,131 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 103 rows
2024-06-17 19:29:17,448 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:29:18,227 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 19:29:19,425 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 19:29:30,274 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 164 rows
2024-06-17 19:29:32,843 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 53 rows
2024-06-17 19:29:33,652 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:29:35,080 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 19:29:35,435 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:29:37,649 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:29:38,621 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 19:29:38,914 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:29:39,252 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:29:39,677 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:29:47,307 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 142 rows
2024-06-17 19:29:47,902 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 19:29:53,529 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 110 rows
2024-06-17 19:29:58,230 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 106 rows
2024-06-17 19:29:59,561 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:30:00,792 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 19:30:01,427 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 19:30:02,210 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:30:02,922 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:30:05,492 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 19:30:06,947 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 19:30:07,509 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:30:09,679 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 39 rows
2024-06-17 19:30:14,977 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 85 rows
2024-06-17 19:30:16,000 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 19:30:16,754 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 19:30:20,523 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 19:30:21,553 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 19:30:22,801 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 19:30:26,855 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 96 rows
2024-06-17 19:30:33,236 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 133 rows
2024-06-17 19:30:34,587 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 19:30:36,766 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:30:37,245 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 19:30:37,735 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 19:30:45,438 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 135 rows
2024-06-17 19:30:48,164 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 19:30:49,041 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:31:01,404 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 285 rows
2024-06-17 19:31:19,256 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 203 rows
2024-06-17 19:31:23,201 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 31 rows
2024-06-17 19:31:23,541 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:31:23,868 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:31:24,464 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:31:25,071 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:31:29,752 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 104 rows
2024-06-17 19:31:30,910 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 19:31:31,558 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:31:32,062 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:31:36,871 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 102 rows
2024-06-17 19:31:40,310 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 54 rows
2024-06-17 19:31:43,155 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 33 rows
2024-06-17 19:31:43,713 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:32:01,453 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 363 rows
2024-06-17 19:32:01,898 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:32:04,586 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 46 rows
2024-06-17 19:32:07,339 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 31 rows
2024-06-17 19:32:07,625 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:32:09,662 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 19:32:14,649 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 81 rows
2024-06-17 19:32:14,964 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:32:15,317 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:32:15,716 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:32:16,103 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:32:19,834 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 54 rows
2024-06-17 19:32:27,991 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 67 rows
2024-06-17 19:32:28,299 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:32:30,128 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 39 rows
2024-06-17 19:32:31,138 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 18 rows
2024-06-17 19:32:31,835 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-17 19:32:33,691 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:32:36,376 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 19:32:36,984 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:32:39,921 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 69 rows
2024-06-17 19:32:42,195 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 25 rows
2024-06-17 19:32:44,365 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:32:48,619 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 93 rows
2024-06-17 19:32:49,446 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 19:32:51,118 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:32:57,250 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 110 rows
2024-06-17 19:32:58,053 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 19:32:58,842 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 19:33:04,830 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 124 rows
2024-06-17 19:33:06,743 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 19:33:07,752 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 19:33:55,980 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 702 rows
2024-06-17 19:33:56,856 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 19:33:57,305 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 19:33:59,809 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 44 rows
2024-06-17 19:34:01,113 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 19:34:01,733 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 19:34:03,757 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:34:08,468 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 59 rows
2024-06-17 19:34:10,641 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 43 rows
2024-06-17 19:34:11,978 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 19:34:13,634 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 19:34:17,026 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 51 rows
2024-06-17 19:34:17,715 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:34:32,508 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 293 rows
2024-06-17 19:34:33,630 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:34:40,283 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 107 rows
2024-06-17 19:34:43,077 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 40 rows
2024-06-17 19:34:44,776 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 24 rows
2024-06-17 19:34:51,023 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 128 rows
2024-06-17 19:34:57,625 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 142 rows
2024-06-17 19:35:05,592 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 99 rows
2024-06-17 19:35:06,493 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-17 19:35:14,454 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 115 rows
2024-06-17 19:35:15,009 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 19:35:25,407 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 166 rows
2024-06-17 19:35:27,542 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 42 rows
2024-06-17 19:35:28,124 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 19:35:30,066 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 47 rows
2024-06-17 19:35:30,552 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:35:59,905 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 503 rows
2024-06-17 19:36:01,594 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 19:36:02,611 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:36:04,542 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:36:07,318 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 19:36:08,395 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 19:37:05,871 ｜ INFO ｜ milvus_helpers.py ｜ __init__ ｜ 14 ｜ Successfully connect to Milvus with IP:************* and PORT:19530
2024-06-17 19:37:08,055 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 19:37:09,036 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:37:25,176 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 89 rows
2024-06-17 19:37:40,336 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 133 rows
2024-06-17 19:37:47,458 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 55 rows
2024-06-17 19:37:48,890 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:37:55,816 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 59 rows
2024-06-17 19:37:56,724 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 19:37:57,504 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:37:58,544 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 19:37:59,625 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 19:38:00,060 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:38:01,494 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 19:38:09,760 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 56 rows
2024-06-17 19:38:19,262 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 66 rows
2024-06-17 19:39:02,043 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 373 rows
2024-06-17 19:39:26,913 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 248 rows
2024-06-17 19:39:28,847 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 19:39:29,497 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:39:34,096 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 44 rows
2024-06-17 19:39:50,153 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 128 rows
2024-06-17 19:39:53,016 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 26 rows
2024-06-17 19:40:04,757 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 131 rows
2024-06-17 19:40:05,258 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:40:23,578 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 149 rows
2024-06-17 19:40:24,231 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:40:38,746 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 148 rows
2024-06-17 19:40:39,646 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:40:42,863 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 19:40:45,169 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 19:40:47,455 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 19:40:53,553 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 40 rows
2024-06-17 19:40:54,178 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:40:55,608 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 19:40:56,826 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-17 19:41:00,150 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 40 rows
2024-06-17 19:41:05,077 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 42 rows
2024-06-17 19:41:06,410 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:41:08,698 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 19:41:15,034 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 39 rows
2024-06-17 19:41:24,489 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 76 rows
2024-06-17 19:41:25,039 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:41:25,518 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 19:41:28,051 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 29 rows
2024-06-17 19:41:45,286 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 138 rows
2024-06-17 19:41:55,099 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 70 rows
2024-06-17 19:41:55,704 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:41:57,072 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 19:42:23,936 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 272 rows
2024-06-17 19:42:38,560 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 168 rows
2024-06-17 19:42:45,563 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 48 rows
2024-06-17 19:42:46,371 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 19:42:59,677 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 109 rows
2024-06-17 19:43:07,112 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 70 rows
2024-06-17 19:43:07,671 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:43:14,824 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 52 rows
2024-06-17 19:43:20,898 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 57 rows
2024-06-17 19:43:23,950 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 37 rows
2024-06-17 19:43:59,663 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 357 rows
2024-06-17 19:44:07,642 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 75 rows
2024-06-17 19:44:08,116 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 19:44:09,759 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-17 19:44:11,632 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:44:25,306 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 133 rows
2024-06-17 19:44:38,840 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 136 rows
2024-06-17 19:44:49,087 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 83 rows
2024-06-17 19:44:51,699 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 19:44:56,724 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 58 rows
2024-06-17 19:45:00,822 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 47 rows
2024-06-17 19:45:13,909 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 98 rows
2024-06-17 19:45:14,326 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 19:45:19,680 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 52 rows
2024-06-17 19:45:22,022 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 19:45:23,429 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 19:45:24,254 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:45:25,139 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 19:45:26,093 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 19:45:36,718 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 120 rows
2024-06-17 19:45:37,732 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 19:45:47,069 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 76 rows
2024-06-17 19:45:58,073 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 123 rows
2024-06-17 19:46:02,680 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 50 rows
2024-06-17 19:46:09,166 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 57 rows
2024-06-17 19:46:17,853 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 81 rows
2024-06-17 19:46:21,536 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 44 rows
2024-06-17 19:46:25,308 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 41 rows
2024-06-17 19:46:44,933 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 166 rows
2024-06-17 19:46:46,983 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 19:46:47,555 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:46:50,130 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 19:47:17,337 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 247 rows
2024-06-17 19:47:21,238 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 19:47:23,217 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 19:47:24,959 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 19:47:30,078 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 59 rows
2024-06-17 19:47:45,738 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 127 rows
2024-06-17 19:47:46,275 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:47:47,714 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:47:49,313 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:47:51,462 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 19:48:08,812 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 172 rows
2024-06-17 19:48:22,988 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 141 rows
2024-06-17 19:48:50,008 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 241 rows
2024-06-17 19:48:53,631 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 19:48:54,557 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 19:48:56,627 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 19:49:01,365 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 57 rows
2024-06-17 19:49:06,595 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 47 rows
2024-06-17 19:49:15,661 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 88 rows
2024-06-17 19:49:27,560 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 128 rows
2024-06-17 19:49:30,006 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 27 rows
2024-06-17 19:49:33,241 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 19:49:39,984 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 33 rows
2024-06-17 19:49:41,806 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 19:50:11,414 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 272 rows
2024-06-17 19:50:12,679 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:50:14,277 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 19:50:21,197 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 70 rows
2024-06-17 19:50:21,765 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 19:50:22,611 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 19:50:24,558 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 19:50:30,534 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 67 rows
2024-06-17 19:50:35,701 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 32 rows
2024-06-17 19:50:36,454 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 19:50:42,372 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 57 rows
2024-06-17 19:50:44,722 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 28 rows
2024-06-17 19:50:45,770 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 19:50:57,721 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 108 rows
2024-06-17 19:51:01,070 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 33 rows
2024-06-17 19:51:15,634 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 97 rows
2024-06-17 19:51:39,073 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 204 rows
2024-06-17 19:51:56,576 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 160 rows
2024-06-17 19:52:20,608 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 203 rows
2024-06-17 19:52:25,398 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 52 rows
2024-06-17 19:52:26,301 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 20:20:12,644 ｜ INFO ｜ milvus_helpers.py ｜ __init__ ｜ 14 ｜ Successfully connect to Milvus with IP:************* and PORT:19530
2024-06-17 20:20:16,215 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 20:20:19,979 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 37 rows
2024-06-17 20:20:30,136 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 214 rows
2024-06-17 20:20:34,634 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 56 rows
2024-06-17 20:20:40,994 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 74 rows
2024-06-17 20:21:38,713 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 449 rows
2024-06-17 20:21:39,004 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 20:21:42,325 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 20:22:32,028 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 954 rows
2024-06-17 20:22:45,921 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 119 rows
2024-06-17 20:22:52,214 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 58 rows
2024-06-17 20:22:54,246 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 39 rows
2024-06-17 20:22:56,806 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 47 rows
2024-06-17 20:22:57,834 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 20:22:58,864 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 20:23:00,906 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 44 rows
2024-06-17 20:23:05,520 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 18 rows
2024-06-17 20:23:09,015 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 27 rows
2024-06-17 20:23:09,999 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-17 20:23:41,048 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 573 rows
2024-06-17 20:23:44,319 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 31 rows
2024-06-17 20:23:48,470 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 53 rows
2024-06-17 20:23:52,795 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 77 rows
2024-06-17 20:23:53,481 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 20:23:56,322 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 24 rows
2024-06-17 20:23:59,518 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 70 rows
2024-06-17 20:24:01,955 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 44 rows
2024-06-17 20:24:05,914 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 33 rows
2024-06-17 20:25:45,957 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 331 rows
2024-06-17 20:25:46,936 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 20:25:59,998 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 225 rows
2024-06-17 20:26:01,814 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 20:27:12,395 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 734 rows
2024-06-17 20:28:37,725 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 793 rows
2024-06-17 20:28:38,298 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 20:28:43,555 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 29 rows
2024-06-17 20:28:52,704 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 122 rows
2024-06-17 20:30:52,686 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 506 rows
2024-06-17 20:30:54,218 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 33 rows
2024-06-17 20:31:01,076 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 132 rows
2024-06-17 20:31:07,104 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 67 rows
2024-06-17 20:31:07,526 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 20:32:37,328 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 954 rows
2024-06-17 20:32:38,112 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 20:32:42,590 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 20:32:44,250 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 20:32:45,519 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 20:32:56,449 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 32 rows
2024-06-17 20:33:03,787 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 122 rows
2024-06-17 20:33:05,639 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 20:33:09,207 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 49 rows
2024-06-17 20:33:10,553 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 18 rows
2024-06-17 20:33:15,982 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 44 rows
2024-06-17 20:33:32,434 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 107 rows
2024-06-17 20:33:35,586 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 20:33:37,374 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 18 rows
2024-06-17 20:33:37,997 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 20:33:43,474 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 84 rows
2024-06-17 20:34:05,157 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 225 rows
2024-06-17 20:34:07,399 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 51 rows
2024-06-17 20:34:16,169 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 118 rows
2024-06-17 20:34:20,181 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 40 rows
2024-06-17 20:36:06,471 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 370 rows
2024-06-17 20:36:22,895 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 196 rows
2024-06-17 20:36:23,426 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 20:36:26,101 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 62 rows
2024-06-17 20:36:27,897 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 20:36:31,624 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 68 rows
2024-06-17 20:36:33,982 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 20:36:38,104 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 34 rows
2024-06-17 20:36:43,722 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 89 rows
2024-06-17 20:36:49,585 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 121 rows
2024-06-17 20:37:47,258 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 475 rows
2024-06-17 20:37:54,897 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 78 rows
2024-06-17 20:37:58,692 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 37 rows
2024-06-17 20:38:02,563 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 59 rows
2024-06-17 20:38:08,292 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 87 rows
2024-06-17 20:38:09,730 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 27 rows
2024-06-17 20:38:10,021 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 20:39:04,591 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 431 rows
2024-06-17 20:39:16,010 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 78 rows
2024-06-17 20:39:35,995 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 409 rows
2024-06-17 20:39:36,540 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 20:40:19,773 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 769 rows
2024-06-17 20:40:21,183 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 25 rows
2024-06-17 20:40:31,576 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 147 rows
2024-06-17 20:40:40,874 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 118 rows
2024-06-17 20:40:49,743 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 84 rows
2024-06-17 20:40:59,303 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 92 rows
2024-06-17 20:41:08,320 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 68 rows
2024-06-17 20:41:12,704 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 86 rows
2024-06-17 20:41:19,044 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 123 rows
2024-06-17 20:41:34,361 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 128 rows
2024-06-17 20:41:35,095 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 20:41:37,176 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 20:41:38,002 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-17 20:41:42,225 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 51 rows
2024-06-17 20:41:47,555 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 56 rows
2024-06-17 20:41:59,311 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 93 rows
2024-06-17 20:43:02,184 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 579 rows
2024-06-17 20:43:21,421 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 268 rows
2024-06-17 20:43:25,275 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 20:43:29,273 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 46 rows
2024-06-17 20:43:34,047 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 81 rows
2024-06-17 20:43:39,317 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 31 rows
2024-06-17 20:43:49,490 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 144 rows
2024-06-17 20:44:11,778 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 199 rows
2024-06-17 20:44:16,845 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 65 rows
2024-06-17 20:44:20,241 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 20:44:51,056 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 555 rows
2024-06-17 20:45:10,712 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 211 rows
2024-06-17 20:46:16,650 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1290 rows
2024-06-17 20:46:22,590 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 46 rows
2024-06-17 20:46:23,915 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 20:46:36,608 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 135 rows
2024-06-17 20:46:39,731 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 46 rows
2024-06-17 20:46:41,059 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 26 rows
2024-06-17 20:46:42,598 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 26 rows
2024-06-17 20:46:50,752 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 57 rows
2024-06-17 20:46:51,517 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 20:46:56,751 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 121 rows
2024-06-17 20:46:58,337 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 33 rows
2024-06-17 20:47:05,929 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 39 rows
2024-06-17 20:47:12,218 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 72 rows
2024-06-17 20:47:38,621 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 535 rows
2024-06-17 20:47:41,051 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 20:48:28,826 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 769 rows
2024-06-17 20:48:30,562 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 34 rows
2024-06-17 20:48:33,741 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 60 rows
2024-06-17 20:48:54,352 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 209 rows
2024-06-17 20:48:57,420 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 52 rows
2024-06-17 20:49:02,118 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 98 rows
2024-06-17 20:49:15,812 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 98 rows
2024-06-17 20:49:21,133 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 60 rows
2024-06-17 20:49:31,921 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 248 rows
2024-06-17 20:49:38,274 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 79 rows
2024-06-17 20:49:40,635 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 20:49:45,954 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 79 rows
2024-06-17 20:49:47,547 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 29 rows
2024-06-17 20:50:01,534 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 139 rows
2024-06-17 20:50:15,355 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 126 rows
2024-06-17 20:50:17,460 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 52 rows
2024-06-17 20:50:23,929 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 123 rows
2024-06-17 20:51:29,460 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1253 rows
2024-06-17 20:52:26,309 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1095 rows
2024-06-17 20:52:28,827 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 45 rows
2024-06-17 20:52:30,620 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 20:53:29,270 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1089 rows
2024-06-17 20:53:32,582 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 77 rows
2024-06-17 20:53:35,898 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 29 rows
2024-06-17 20:53:37,398 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 20:53:39,833 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 39 rows
2024-06-17 20:53:46,580 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 120 rows
2024-06-17 20:53:50,384 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 57 rows
2024-06-17 20:53:52,826 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 52 rows
2024-06-17 20:53:59,526 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 161 rows
2024-06-17 20:54:17,105 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 194 rows
2024-06-17 20:54:21,064 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 85 rows
2024-06-17 20:54:24,595 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 37 rows
2024-06-17 20:54:27,180 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 58 rows
2024-06-17 20:54:27,466 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 20:54:32,992 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 18 rows
2024-06-17 20:54:48,301 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 159 rows
2024-06-17 20:54:49,147 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 20:55:03,039 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 289 rows
2024-06-17 20:55:08,691 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 72 rows
2024-06-17 20:55:14,609 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 20:55:14,908 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 20:55:28,062 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 175 rows
2024-06-17 20:56:04,955 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 620 rows
2024-06-17 20:56:06,866 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 20:56:11,296 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 93 rows
2024-06-17 20:56:15,325 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 20:57:55,450 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 879 rows
2024-06-17 20:57:58,108 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 51 rows
2024-06-17 20:59:22,337 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 800 rows
2024-06-17 20:59:24,023 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 31 rows
2024-06-17 20:59:34,479 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 183 rows
2024-06-17 20:59:38,924 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 69 rows
2024-06-17 21:00:01,434 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 141 rows
2024-06-17 21:00:08,002 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 136 rows
2024-06-17 21:01:52,097 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1055 rows
2024-06-17 21:01:56,122 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 45 rows
2024-06-17 21:02:03,242 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 92 rows
2024-06-17 21:02:07,050 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 45 rows
2024-06-17 21:03:07,494 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 509 rows
2024-06-17 21:03:09,882 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 21:03:14,404 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 33 rows
2024-06-17 21:03:16,944 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 21:03:19,730 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 21:03:26,714 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 134 rows
2024-06-17 21:03:29,120 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 56 rows
2024-06-17 21:03:31,026 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 21:03:39,995 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 131 rows
2024-06-17 21:03:45,271 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 69 rows
2024-06-17 21:03:48,000 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 21:03:53,282 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 53 rows
2024-06-17 21:04:00,705 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 139 rows
2024-06-17 21:04:24,721 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 298 rows
2024-06-17 21:04:28,203 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 75 rows
2024-06-17 21:04:37,272 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 121 rows
2024-06-17 21:04:38,406 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 21:04:41,638 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 21:04:43,942 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 40 rows
2024-06-17 21:04:48,662 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 59 rows
2024-06-17 21:04:52,567 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 74 rows
2024-06-17 21:04:54,014 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 21:05:01,587 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 149 rows
2024-06-17 21:05:03,429 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 21:05:08,717 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 77 rows
2024-06-17 21:05:09,638 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 18 rows
2024-06-17 21:05:15,757 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 115 rows
2024-06-17 21:05:17,846 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 34 rows
2024-06-17 21:05:23,710 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 58 rows
2024-06-17 21:05:25,609 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 21:05:27,345 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 33 rows
2024-06-17 21:05:27,626 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 21:05:29,317 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 34 rows
2024-06-17 21:05:35,246 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 60 rows
2024-06-17 21:05:38,867 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 77 rows
2024-06-17 21:07:24,193 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 371 rows
2024-06-17 21:07:38,455 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 166 rows
2024-06-17 21:08:16,436 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 359 rows
2024-06-17 21:08:19,217 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 45 rows
2024-06-17 21:08:19,935 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 21:08:21,553 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 28 rows
2024-06-17 21:08:26,399 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 110 rows
2024-06-17 21:08:26,738 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 21:08:27,805 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 19 rows
2024-06-17 21:08:28,093 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 21:08:44,601 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 142 rows
2024-06-17 21:08:47,005 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 31 rows
2024-06-17 21:08:52,875 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 60 rows
2024-06-17 21:08:58,583 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 106 rows
2024-06-17 21:09:05,535 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 21:09:08,845 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 39 rows
2024-06-17 21:09:15,387 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 83 rows
2024-06-17 21:09:27,360 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 65 rows
2024-06-17 21:09:32,394 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 93 rows
2024-06-17 21:09:38,779 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 69 rows
2024-06-17 21:12:05,318 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1753 rows
2024-06-17 21:12:15,313 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 142 rows
2024-06-17 21:12:23,800 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 115 rows
2024-06-17 21:12:26,643 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 62 rows
2024-06-17 21:12:29,803 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 27 rows
2024-06-17 21:12:38,328 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 120 rows
2024-06-17 21:12:39,732 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 26 rows
2024-06-17 21:12:41,639 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 45 rows
2024-06-17 21:13:14,298 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 427 rows
2024-06-17 21:13:19,765 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 65 rows
2024-06-17 21:13:57,819 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 433 rows
2024-06-17 21:14:04,849 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 37 rows
2024-06-17 21:14:40,413 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 558 rows
2024-06-17 21:15:24,086 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 389 rows
2024-06-17 21:15:27,120 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 52 rows
2024-06-17 21:16:46,712 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 761 rows
2024-06-17 21:16:50,507 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 21:16:51,768 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 21:16:54,942 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 67 rows
2024-06-17 21:17:04,328 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 131 rows
2024-06-17 21:17:57,043 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 884 rows
2024-06-17 21:17:58,549 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 21:18:00,888 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 45 rows
2024-06-17 21:18:02,236 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 21:18:17,184 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 138 rows
2024-06-17 21:18:19,438 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 21:18:25,122 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 97 rows
2024-06-17 21:18:25,431 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 21:18:32,943 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 145 rows
2024-06-17 21:18:33,374 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 21:18:43,967 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 116 rows
2024-06-17 21:18:49,715 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 94 rows
2024-06-17 21:18:53,603 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 21:18:57,789 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 97 rows
2024-06-17 21:18:58,415 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 21:18:59,838 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 21:19:04,393 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 65 rows
2024-06-17 21:19:07,358 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 64 rows
2024-06-17 21:19:11,502 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 44 rows
2024-06-17 21:19:14,202 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 21:19:19,473 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 106 rows
2024-06-17 21:20:50,054 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 925 rows
2024-06-17 21:20:57,205 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 129 rows
2024-06-17 21:21:00,615 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 21:21:10,097 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 73 rows
2024-06-17 21:21:10,385 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 21:21:17,275 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 111 rows
2024-06-17 21:21:23,824 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 97 rows
2024-06-17 21:21:27,643 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 47 rows
2024-06-17 21:21:31,169 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 63 rows
2024-06-17 21:21:33,515 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 24 rows
2024-06-17 21:21:35,272 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 21:21:40,441 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 64 rows
2024-06-17 21:21:58,152 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 183 rows
2024-06-17 21:22:00,869 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 57 rows
2024-06-17 21:23:27,462 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 815 rows
2024-06-17 21:23:27,978 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 21:24:19,960 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1036 rows
2024-06-17 21:24:23,592 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 34 rows
2024-06-17 21:24:28,159 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 73 rows
2024-06-17 21:24:29,953 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 41 rows
2024-06-17 21:24:36,616 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 61 rows
2024-06-17 21:24:38,787 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 41 rows
2024-06-17 21:25:16,467 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 674 rows
2024-06-17 21:25:21,994 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 118 rows
2024-06-17 21:25:24,273 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 44 rows
2024-06-17 21:25:26,010 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 21:25:28,124 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 38 rows
2024-06-17 21:25:29,233 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 21:25:30,349 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 21:25:30,624 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 21:25:30,907 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 21:25:33,878 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 34 rows
2024-06-17 21:25:48,114 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 201 rows
2024-06-17 21:25:53,268 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 52 rows
2024-06-17 21:25:54,738 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 21:25:56,762 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 42 rows
2024-06-17 21:26:00,230 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 62 rows
2024-06-17 21:26:02,493 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 21:26:03,178 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 21:26:12,034 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 118 rows
2024-06-17 21:26:15,345 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 21:26:18,024 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 60 rows
2024-06-17 21:26:22,074 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 42 rows
2024-06-17 21:26:27,130 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 87 rows
2024-06-17 21:26:32,245 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 112 rows
2024-06-17 21:26:40,523 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 133 rows
2024-06-17 21:27:03,258 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 288 rows
2024-06-17 21:27:09,626 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 79 rows
2024-06-17 21:27:22,414 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 123 rows
2024-06-17 21:27:29,956 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 58 rows
2024-06-17 21:27:34,750 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 75 rows
2024-06-17 21:27:39,986 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 40 rows
2024-06-17 21:27:41,208 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 21:27:53,010 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 108 rows
2024-06-17 21:28:01,760 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 60 rows
2024-06-17 21:28:05,436 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 49 rows
2024-06-17 21:28:14,612 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 68 rows
2024-06-17 21:28:31,787 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 161 rows
2024-06-17 21:28:39,393 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 80 rows
2024-06-17 21:29:09,662 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 293 rows
2024-06-17 21:29:12,750 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 21:29:14,813 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 43 rows
2024-06-17 21:29:29,186 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 193 rows
2024-06-17 21:29:30,198 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 18 rows
2024-06-17 21:29:39,697 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 103 rows
2024-06-17 21:29:42,758 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 21:29:57,483 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 278 rows
2024-06-17 21:30:38,848 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 483 rows
2024-06-17 21:30:46,645 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 126 rows
2024-06-17 21:31:04,368 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 300 rows
2024-06-17 21:31:11,610 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 106 rows
2024-06-17 21:31:14,588 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-17 21:31:25,574 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 169 rows
2024-06-17 21:31:28,331 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 64 rows
2024-06-17 21:31:36,420 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 134 rows
2024-06-17 21:31:45,021 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 92 rows
2024-06-17 21:31:51,173 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 50 rows
2024-06-17 21:32:00,179 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 178 rows
2024-06-17 21:32:02,943 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 32 rows
2024-06-17 21:32:05,247 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 21:32:07,006 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 21:32:07,361 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 21:32:10,422 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 58 rows
2024-06-17 21:32:13,691 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 38 rows
2024-06-17 21:32:17,388 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 21:33:21,109 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 715 rows
2024-06-17 21:33:27,465 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 144 rows
2024-06-17 21:33:30,316 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 21:33:38,438 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 21:33:38,856 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 21:33:42,242 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 45 rows
2024-06-17 21:33:44,004 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 21:33:48,572 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 68 rows
2024-06-17 21:34:36,288 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 532 rows
2024-06-17 21:34:37,240 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 21:34:40,743 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 21:34:42,456 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 21:34:51,109 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 114 rows
2024-06-17 21:34:54,374 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 80 rows
2024-06-17 21:34:56,195 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 39 rows
2024-06-17 21:35:47,034 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 858 rows
2024-06-17 21:35:53,642 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 108 rows
2024-06-17 21:35:55,670 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 51 rows
2024-06-17 21:35:58,178 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 21:35:59,294 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 21:35:59,635 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 21:36:03,620 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 58 rows
2024-06-17 21:36:07,344 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 21:37:43,414 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 979 rows
2024-06-17 21:37:52,213 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 96 rows
2024-06-17 21:37:53,792 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 27 rows
2024-06-17 21:39:05,512 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 573 rows
2024-06-17 21:39:14,381 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 91 rows
2024-06-17 21:41:01,891 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1060 rows
2024-06-17 21:41:04,771 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 21:41:11,116 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 71 rows
2024-06-17 21:41:19,356 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 98 rows
2024-06-17 21:41:31,519 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 134 rows
2024-06-17 21:42:33,160 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1309 rows
2024-06-17 21:42:37,779 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 21:42:38,847 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 19 rows
2024-06-17 21:42:50,554 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 120 rows
2024-06-17 21:43:06,997 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 237 rows
2024-06-17 21:43:18,665 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 154 rows
2024-06-17 21:43:26,743 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 93 rows
2024-06-17 21:43:36,627 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 34 rows
2024-06-17 21:43:42,397 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 48 rows
2024-06-17 21:43:48,404 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 51 rows
2024-06-17 21:43:48,865 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 21:43:50,934 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 46 rows
2024-06-17 21:43:52,516 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 33 rows
2024-06-17 21:43:53,593 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 21:43:56,182 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 31 rows
2024-06-17 21:43:59,523 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 60 rows
2024-06-17 21:44:04,853 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 46 rows
2024-06-17 21:45:33,854 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 951 rows
2024-06-17 21:45:42,235 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 76 rows
2024-06-17 21:45:44,460 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 21:45:47,254 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 21:47:18,815 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 300 rows
2024-06-17 21:47:20,293 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 21:47:20,578 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 21:47:22,428 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 38 rows
2024-06-17 21:47:28,931 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 81 rows
2024-06-17 21:47:54,021 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 423 rows
2024-06-17 21:47:56,150 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 34 rows
2024-06-17 21:48:00,995 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 97 rows
2024-06-17 21:48:03,256 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 21:48:04,824 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 21:48:31,521 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 364 rows
2024-06-17 21:48:32,066 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 21:49:37,322 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 683 rows
2024-06-17 21:49:40,120 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 21:49:44,140 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 51 rows
2024-06-17 21:49:47,659 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 55 rows
2024-06-17 21:49:50,075 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 49 rows
2024-06-17 21:49:52,786 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 21:49:58,133 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 129 rows
2024-06-17 21:50:01,827 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 84 rows
2024-06-17 21:50:29,281 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 375 rows
2024-06-17 21:50:29,988 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 21:50:32,162 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 51 rows
2024-06-17 21:51:16,758 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 867 rows
2024-06-17 21:51:20,876 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 50 rows
2024-06-17 21:51:29,281 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 64 rows
2024-06-17 21:51:30,685 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 24 rows
2024-06-17 21:52:30,094 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 982 rows
2024-06-17 21:52:33,515 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 21:52:41,694 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 79 rows
2024-06-17 21:52:42,865 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 21:52:47,689 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 72 rows
2024-06-17 21:52:48,233 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 21:52:50,371 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 21:52:51,928 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 26 rows
2024-06-17 21:53:45,465 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 396 rows
2024-06-17 21:55:20,509 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 929 rows
2024-06-17 21:55:23,294 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 40 rows
2024-06-17 21:55:32,150 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 66 rows
2024-06-17 21:55:41,613 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 149 rows
2024-06-17 21:55:50,269 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 116 rows
2024-06-17 21:55:57,209 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 116 rows
2024-06-17 21:56:16,980 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 176 rows
2024-06-17 21:56:21,669 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 88 rows
2024-06-17 21:56:23,150 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-17 21:57:44,081 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1699 rows
2024-06-17 21:57:47,513 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 69 rows
2024-06-17 21:57:54,810 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 63 rows
2024-06-17 21:58:18,357 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 275 rows
2024-06-17 21:58:22,507 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 42 rows
2024-06-17 21:59:59,286 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 942 rows
2024-06-17 22:00:02,010 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 22:00:07,038 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 43 rows
2024-06-17 22:00:09,529 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 22:00:11,665 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 22:00:14,337 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 22:00:18,649 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 22:00:22,556 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 38 rows
2024-06-17 22:00:29,038 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 41 rows
2024-06-17 22:01:02,007 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 394 rows
2024-06-17 22:01:08,554 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 38 rows
2024-06-17 22:01:10,873 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 22:01:15,346 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 45 rows
2024-06-17 22:01:15,729 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 22:01:21,207 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 55 rows
2024-06-17 22:01:25,026 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 44 rows
2024-06-17 22:01:31,174 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 77 rows
2024-06-17 22:01:39,117 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 113 rows
2024-06-17 22:01:40,802 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 22:01:49,583 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 104 rows
2024-06-17 22:02:16,581 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 270 rows
2024-06-17 22:02:24,170 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 76 rows
2024-06-17 22:03:32,459 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 895 rows
2024-06-17 22:03:36,517 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 22:03:37,419 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 18 rows
2024-06-17 22:03:45,127 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 34 rows
2024-06-17 22:03:47,593 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 22:03:51,543 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 31 rows
2024-06-17 22:03:56,843 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 66 rows
2024-06-17 22:03:57,906 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 22:04:00,210 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 38 rows
2024-06-17 22:04:03,530 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 40 rows
2024-06-17 22:04:22,976 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 146 rows
2024-06-17 22:04:27,036 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 53 rows
2024-06-17 22:04:33,218 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 110 rows
2024-06-17 22:04:37,383 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 41 rows
2024-06-17 22:04:40,602 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 33 rows
2024-06-17 22:04:50,447 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 95 rows
2024-06-17 22:05:01,150 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 145 rows
2024-06-17 22:05:01,443 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 22:05:15,752 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 116 rows
2024-06-17 22:05:20,757 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 75 rows
2024-06-17 22:05:57,214 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 360 rows
2024-06-17 22:05:58,175 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 18 rows
2024-06-17 22:06:02,726 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 79 rows
2024-06-17 22:06:11,469 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 64 rows
2024-06-17 22:06:20,111 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 77 rows
2024-06-17 22:06:23,986 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 54 rows
2024-06-17 22:07:26,210 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 593 rows
2024-06-17 22:07:29,764 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 44 rows
2024-06-17 22:07:32,919 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 22:07:33,457 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 22:07:56,824 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 327 rows
2024-06-17 22:08:01,799 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 104 rows
2024-06-17 22:08:02,099 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 22:08:07,246 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 58 rows
2024-06-17 22:08:11,961 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 22:09:07,452 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 555 rows
2024-06-17 22:09:16,719 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 79 rows
2024-06-17 22:09:17,003 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 22:09:21,563 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 47 rows
2024-06-17 22:09:27,573 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 114 rows
2024-06-17 22:09:27,859 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 22:10:02,370 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 524 rows
2024-06-17 22:10:11,511 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 49 rows
2024-06-17 22:10:19,755 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 113 rows
2024-06-17 22:10:23,476 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 37 rows
2024-06-17 22:10:27,602 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 62 rows
2024-06-17 22:10:29,293 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 22:10:35,197 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 75 rows
2024-06-17 22:10:39,801 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 19 rows
2024-06-17 22:10:49,855 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 95 rows
2024-06-17 22:10:50,170 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 22:10:56,655 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 89 rows
2024-06-17 22:11:15,228 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 265 rows
2024-06-17 22:11:22,545 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 105 rows
2024-06-17 22:11:24,119 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 18 rows
2024-06-17 22:11:29,852 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 92 rows
2024-06-17 22:11:33,241 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 41 rows
2024-06-17 22:11:37,842 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 22:11:46,885 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 135 rows
2024-06-17 22:13:43,243 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 958 rows
2024-06-17 22:13:45,499 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 22:13:46,133 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 22:13:53,302 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 80 rows
2024-06-17 22:13:54,604 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 22:14:10,184 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 137 rows
2024-06-17 22:14:14,280 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 48 rows
2024-06-17 22:14:14,957 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 22:14:17,844 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 47 rows
2024-06-17 22:14:23,897 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 48 rows
2024-06-17 22:14:29,486 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 22:14:31,671 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 22:14:36,551 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 44 rows
2024-06-17 22:16:02,624 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1420 rows
2024-06-17 22:16:14,941 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 29 rows
2024-06-17 22:16:26,132 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 126 rows
2024-06-17 22:16:29,775 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 84 rows
2024-06-17 22:16:31,805 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 22:16:39,521 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 76 rows
2024-06-17 22:16:51,629 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 168 rows
2024-06-17 22:16:53,871 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 26 rows
2024-06-17 22:16:59,180 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 97 rows
2024-06-17 22:17:21,263 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 339 rows
2024-06-17 22:17:21,991 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 22:17:29,651 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 93 rows
2024-06-17 22:17:37,651 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 97 rows
2024-06-17 22:17:39,303 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 25 rows
2024-06-17 22:17:47,793 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 106 rows
2024-06-17 22:17:55,548 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 85 rows
2024-06-17 22:18:10,086 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 181 rows
2024-06-17 22:19:11,858 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 919 rows
2024-06-17 22:19:13,531 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 22:19:14,888 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 22:19:15,402 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 22:19:19,372 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 72 rows
2024-06-17 22:19:25,992 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 59 rows
2024-06-17 22:19:29,532 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 28 rows
2024-06-17 22:19:36,176 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 67 rows
2024-06-17 22:20:10,413 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 522 rows
2024-06-17 22:20:16,801 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 48 rows
2024-06-17 22:20:19,457 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 42 rows
2024-06-17 22:20:54,653 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 321 rows
2024-06-17 22:20:56,314 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 33 rows
2024-06-17 22:21:07,864 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 187 rows
2024-06-17 22:22:54,708 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1020 rows
2024-06-17 22:22:58,618 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 62 rows
2024-06-17 22:23:13,439 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 99 rows
2024-06-17 22:23:14,395 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 22:23:16,945 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 42 rows
2024-06-17 22:23:29,600 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 179 rows
2024-06-17 22:25:29,852 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 464 rows
2024-06-17 22:25:31,703 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 43 rows
2024-06-17 22:25:40,086 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 118 rows
2024-06-17 22:25:43,393 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 22:25:51,469 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 86 rows
2024-06-17 22:25:56,823 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 67 rows
2024-06-17 22:25:57,525 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 22:25:59,274 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 22:26:00,901 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 37 rows
2024-06-17 22:26:54,750 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 539 rows
2024-06-17 22:26:56,397 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 24 rows
2024-06-17 22:27:00,800 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 65 rows
2024-06-17 22:27:09,097 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 19 rows
2024-06-17 22:27:09,400 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 22:27:12,388 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 22:27:16,323 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 34 rows
2024-06-17 22:27:22,044 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 75 rows
2024-06-17 22:27:26,410 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 46 rows
2024-06-17 22:27:27,110 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 7 rows
2024-06-17 22:27:29,586 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 22:27:29,870 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 22:27:35,639 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 64 rows
2024-06-17 22:27:38,033 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 29 rows
2024-06-17 22:27:46,296 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 76 rows
2024-06-17 22:27:48,844 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 22:27:53,773 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 59 rows
2024-06-17 22:27:55,548 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 24 rows
2024-06-17 22:27:58,019 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 37 rows
2024-06-17 22:28:01,758 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 70 rows
2024-06-17 22:28:19,731 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 118 rows
2024-06-17 22:28:28,843 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 110 rows
2024-06-17 22:28:29,840 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 19 rows
2024-06-17 22:29:20,394 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 469 rows
2024-06-17 22:29:23,575 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 22:30:41,961 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1225 rows
2024-06-17 22:30:44,501 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 22:30:45,911 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 22:30:47,367 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 26 rows
2024-06-17 22:30:58,764 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 161 rows
2024-06-17 22:31:14,656 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 267 rows
2024-06-17 22:31:18,869 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 61 rows
2024-06-17 22:31:19,181 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 22:31:32,287 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 113 rows
2024-06-17 22:32:12,080 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 346 rows
2024-06-17 22:32:21,198 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 70 rows
2024-06-17 22:32:30,053 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 70 rows
2024-06-17 22:32:30,506 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 22:33:35,410 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 899 rows
2024-06-17 22:33:41,015 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 107 rows
2024-06-17 22:33:56,955 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 212 rows
2024-06-17 22:36:05,891 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 517 rows
2024-06-17 22:36:12,349 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 107 rows
2024-06-17 22:36:14,049 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-17 22:36:21,936 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 74 rows
2024-06-17 22:36:26,969 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 65 rows
2024-06-17 22:36:42,151 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 202 rows
2024-06-17 22:36:43,829 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 9 rows
2024-06-17 22:36:47,794 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 45 rows
2024-06-17 22:36:51,733 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 29 rows
2024-06-17 22:36:53,038 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 22:36:53,662 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 22:36:56,558 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 47 rows
2024-06-17 22:37:00,351 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 73 rows
2024-06-17 22:37:06,141 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 76 rows
2024-06-17 22:37:09,013 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 38 rows
2024-06-17 22:38:48,671 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1756 rows
2024-06-17 22:38:54,060 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 45 rows
2024-06-17 22:40:31,046 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 874 rows
2024-06-17 22:40:32,987 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 27 rows
2024-06-17 22:40:39,193 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 40 rows
2024-06-17 22:41:10,433 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 281 rows
2024-06-17 22:43:09,836 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1207 rows
2024-06-17 22:43:50,994 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 594 rows
2024-06-17 22:43:51,582 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 22:43:53,490 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 27 rows
2024-06-17 22:43:59,114 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 99 rows
2024-06-17 22:43:59,580 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 22:43:59,883 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 22:44:04,491 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 62 rows
2024-06-17 22:44:15,940 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 156 rows
2024-06-17 22:44:54,890 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 330 rows
2024-06-17 22:47:04,895 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1217 rows
2024-06-17 22:47:10,646 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 111 rows
2024-06-17 22:47:20,484 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 139 rows
2024-06-17 22:49:03,488 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 974 rows
2024-06-17 22:49:16,381 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 125 rows
2024-06-17 22:49:24,934 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 99 rows
2024-06-17 22:49:43,060 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 227 rows
2024-06-17 22:50:16,890 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 532 rows
2024-06-17 22:50:22,352 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 55 rows
2024-06-17 22:50:28,821 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 97 rows
2024-06-17 22:50:31,407 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 53 rows
2024-06-17 22:50:58,427 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 204 rows
2024-06-17 22:51:02,137 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 65 rows
2024-06-17 22:51:03,640 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 22:51:12,788 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 24 rows
2024-06-17 22:51:23,680 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 145 rows
2024-06-17 22:51:25,674 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 26 rows
2024-06-17 22:51:41,121 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 204 rows
2024-06-17 22:51:48,743 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 72 rows
2024-06-17 22:51:51,652 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 39 rows
2024-06-17 22:51:54,781 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 22:51:57,804 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 38 rows
2024-06-17 22:51:59,914 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 22:52:08,576 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 118 rows
2024-06-17 22:52:10,385 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 22:52:13,058 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 22:52:14,182 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 22:52:25,477 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 158 rows
2024-06-17 22:52:46,786 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 170 rows
2024-06-17 22:52:49,261 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 28 rows
2024-06-17 22:52:52,672 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 34 rows
2024-06-17 22:52:54,640 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 33 rows
2024-06-17 22:52:57,102 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 43 rows
2024-06-17 22:53:02,052 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 34 rows
2024-06-17 22:53:07,571 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 65 rows
2024-06-17 22:53:16,334 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 126 rows
2024-06-17 22:53:17,209 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 22:53:19,257 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 22:53:25,679 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 78 rows
2024-06-17 22:53:28,563 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 22:53:34,503 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 44 rows
2024-06-17 22:54:35,045 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 944 rows
2024-06-17 22:54:41,978 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 108 rows
2024-06-17 22:54:42,268 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 22:54:43,875 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 22:54:47,929 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 22:54:50,595 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 22:54:57,759 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 107 rows
2024-06-17 22:58:31,876 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2092 rows
2024-06-17 22:58:34,638 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 22:58:40,275 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 77 rows
2024-06-17 22:58:44,449 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 22:58:52,786 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 35 rows
2024-06-17 22:58:58,644 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 107 rows
2024-06-17 22:59:27,493 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 412 rows
2024-06-17 22:59:28,858 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 22:59:30,844 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 38 rows
2024-06-17 22:59:32,177 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 23:00:22,060 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 488 rows
2024-06-17 23:02:01,544 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1756 rows
2024-06-17 23:03:18,033 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1294 rows
2024-06-17 23:03:51,968 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 514 rows
2024-06-17 23:03:55,971 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 55 rows
2024-06-17 23:03:57,346 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 23:04:01,971 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 110 rows
2024-06-17 23:04:29,357 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 376 rows
2024-06-17 23:04:37,468 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 131 rows
2024-06-17 23:04:38,352 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 14 rows
2024-06-17 23:04:54,371 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 142 rows
2024-06-17 23:04:55,837 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 13 rows
2024-06-17 23:04:56,933 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 23:04:57,219 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 23:04:58,080 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 23:05:16,377 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 170 rows
2024-06-17 23:05:22,909 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 58 rows
2024-06-17 23:05:29,481 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 132 rows
2024-06-17 23:05:35,344 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 78 rows
2024-06-17 23:05:36,972 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 23:05:42,091 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 89 rows
2024-06-17 23:05:42,396 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 23:05:50,212 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 82 rows
2024-06-17 23:06:02,759 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 138 rows
2024-06-17 23:06:08,163 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 70 rows
2024-06-17 23:06:16,372 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 106 rows
2024-06-17 23:06:18,570 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 45 rows
2024-06-17 23:06:22,539 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 23:06:38,123 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 273 rows
2024-06-17 23:06:40,535 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 46 rows
2024-06-17 23:06:48,339 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 71 rows
2024-06-17 23:06:48,819 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 23:06:49,222 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 23:06:52,693 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 23:06:56,445 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 40 rows
2024-06-17 23:07:00,733 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 94 rows
2024-06-17 23:07:07,875 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 74 rows
2024-06-17 23:08:22,116 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 679 rows
2024-06-17 23:08:25,442 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 40 rows
2024-06-17 23:08:31,915 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 106 rows
2024-06-17 23:10:32,442 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1144 rows
2024-06-17 23:10:38,877 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 75 rows
2024-06-17 23:10:46,506 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 51 rows
2024-06-17 23:11:08,656 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 257 rows
2024-06-17 23:11:11,741 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 37 rows
2024-06-17 23:11:45,415 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 258 rows
2024-06-17 23:11:49,585 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 42 rows
2024-06-17 23:11:57,066 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 76 rows
2024-06-17 23:11:59,784 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 56 rows
2024-06-17 23:13:28,996 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1417 rows
2024-06-17 23:13:32,006 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 23:13:36,799 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 42 rows
2024-06-17 23:13:40,000 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 61 rows
2024-06-17 23:13:47,119 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 47 rows
2024-06-17 23:13:50,843 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 23:13:56,259 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 62 rows
2024-06-17 23:14:02,166 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 98 rows
2024-06-17 23:14:02,766 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 23:14:08,589 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 54 rows
2024-06-17 23:14:47,311 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 572 rows
2024-06-17 23:15:07,603 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 326 rows
2024-06-17 23:16:48,636 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 918 rows
2024-06-17 23:16:50,534 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 18 rows
2024-06-17 23:16:56,687 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 28 rows
2024-06-17 23:17:37,188 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 723 rows
2024-06-17 23:18:12,972 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 636 rows
2024-06-17 23:18:25,845 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 156 rows
2024-06-17 23:18:28,244 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 23 rows
2024-06-17 23:18:29,083 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 15 rows
2024-06-17 23:18:32,243 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 47 rows
2024-06-17 23:18:36,467 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 36 rows
2024-06-17 23:18:38,806 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 23:18:39,091 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 23:18:42,028 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 43 rows
2024-06-17 23:18:50,565 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 55 rows
2024-06-17 23:18:57,348 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 65 rows
2024-06-17 23:24:23,754 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3056 rows
2024-06-17 23:24:27,276 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 44 rows
2024-06-17 23:24:48,543 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 178 rows
2024-06-17 23:24:51,316 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 42 rows
2024-06-17 23:24:54,320 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 24 rows
2024-06-17 23:26:08,183 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 709 rows
2024-06-17 23:28:50,399 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1649 rows
2024-06-17 23:28:54,051 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 24 rows
2024-06-17 23:28:57,586 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 69 rows
2024-06-17 23:28:59,205 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 23:29:36,085 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 571 rows
2024-06-17 23:29:41,073 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 29 rows
2024-06-17 23:31:40,952 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1057 rows
2024-06-17 23:31:43,461 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 27 rows
2024-06-17 23:31:58,471 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 131 rows
2024-06-17 23:32:02,155 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 62 rows
2024-06-17 23:32:05,437 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 32 rows
2024-06-17 23:32:10,314 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 83 rows
2024-06-17 23:32:10,658 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 23:32:18,066 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 77 rows
2024-06-17 23:32:33,510 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 219 rows
2024-06-17 23:32:38,137 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 31 rows
2024-06-17 23:32:56,383 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 140 rows
2024-06-17 23:32:58,218 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 20 rows
2024-06-17 23:33:05,803 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 48 rows
2024-06-17 23:33:11,325 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 64 rows
2024-06-17 23:33:18,024 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 84 rows
2024-06-17 23:35:48,535 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1274 rows
2024-06-17 23:36:12,169 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 263 rows
2024-06-17 23:36:20,055 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 69 rows
2024-06-17 23:36:23,244 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 23:36:29,104 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 91 rows
2024-06-17 23:36:31,208 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 41 rows
2024-06-17 23:36:59,723 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 248 rows
2024-06-17 23:37:00,554 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 23:37:03,656 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 30 rows
2024-06-17 23:38:40,300 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1710 rows
2024-06-17 23:38:44,139 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 37 rows
2024-06-17 23:38:44,425 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 23:38:57,068 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 243 rows
2024-06-17 23:38:57,453 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 23:41:25,173 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1233 rows
2024-06-17 23:41:32,521 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 23:41:44,232 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 70 rows
2024-06-17 23:41:46,228 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 33 rows
2024-06-17 23:41:49,669 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 60 rows
2024-06-17 23:41:52,279 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 23:41:59,612 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 102 rows
2024-06-17 23:42:11,319 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 145 rows
2024-06-17 23:42:19,688 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 120 rows
2024-06-17 23:42:26,658 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 130 rows
2024-06-17 23:42:28,368 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 32 rows
2024-06-17 23:42:29,114 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 23:42:31,043 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 47 rows
2024-06-17 23:42:37,960 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 65 rows
2024-06-17 23:42:45,082 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 65 rows
2024-06-17 23:42:46,750 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 23:42:53,232 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 86 rows
2024-06-17 23:43:31,970 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 283 rows
2024-06-17 23:43:38,657 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 38 rows
2024-06-17 23:43:42,828 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 10 rows
2024-06-17 23:43:55,282 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 212 rows
2024-06-17 23:43:58,397 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 23:44:05,358 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 64 rows
2024-06-17 23:44:12,797 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 113 rows
2024-06-17 23:44:15,526 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 23:44:22,670 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 82 rows
2024-06-17 23:44:27,253 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 82 rows
2024-06-17 23:44:37,180 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 113 rows
2024-06-17 23:44:37,539 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 5 rows
2024-06-17 23:44:39,987 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 38 rows
2024-06-17 23:44:47,956 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 111 rows
2024-06-17 23:44:50,510 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 19 rows
2024-06-17 23:44:53,264 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 26 rows
2024-06-17 23:44:55,642 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 12 rows
2024-06-17 23:45:07,517 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 76 rows
2024-06-17 23:45:12,399 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 61 rows
2024-06-17 23:45:19,181 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 106 rows
2024-06-17 23:45:26,198 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 32 rows
2024-06-17 23:45:29,739 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 85 rows
2024-06-17 23:45:33,114 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 27 rows
2024-06-17 23:45:33,621 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 23:45:34,285 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1 rows
2024-06-17 23:45:36,253 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 23:45:38,505 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 34 rows
2024-06-17 23:45:42,163 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 39 rows
2024-06-17 23:45:50,406 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 50 rows
2024-06-17 23:46:10,426 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 207 rows
2024-06-17 23:46:13,204 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 28 rows
2024-06-17 23:46:14,359 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 23:46:16,834 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 23:46:25,090 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 109 rows
2024-06-17 23:46:38,109 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 252 rows
2024-06-17 23:46:38,835 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 23:46:47,959 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 112 rows
2024-06-17 23:46:53,673 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 78 rows
2024-06-17 23:46:54,170 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 23:46:57,184 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 23:46:59,330 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 43 rows
2024-06-17 23:47:01,105 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 22 rows
2024-06-17 23:47:03,562 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 32 rows
2024-06-17 23:47:58,232 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 534 rows
2024-06-17 23:48:07,020 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 115 rows
2024-06-17 23:48:15,295 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 37 rows
2024-06-17 23:48:23,461 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 23:48:31,548 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 78 rows
2024-06-17 23:48:31,828 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 23:49:32,267 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 552 rows
2024-06-17 23:49:32,575 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 3 rows
2024-06-17 23:49:34,068 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 23:49:53,369 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 101 rows
2024-06-17 23:49:58,709 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 71 rows
2024-06-17 23:49:59,311 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 6 rows
2024-06-17 23:50:07,891 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 25 rows
2024-06-17 23:50:08,593 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
2024-06-17 23:50:47,181 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 242 rows
2024-06-17 23:50:48,051 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 17 rows
2024-06-17 23:50:50,184 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 21 rows
2024-06-17 23:50:55,838 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 79 rows
2024-06-17 23:52:28,165 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 876 rows
2024-06-17 23:54:31,655 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 520 rows
2024-06-17 23:54:33,462 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 23:56:31,503 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 1070 rows
2024-06-17 23:56:31,858 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 4 rows
2024-06-17 23:56:40,341 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 123 rows
2024-06-17 23:56:48,856 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 106 rows
2024-06-17 23:56:51,804 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 23:56:57,227 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 62 rows
2024-06-17 23:57:03,274 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 72 rows
2024-06-17 23:57:09,652 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 74 rows
2024-06-17 23:57:10,349 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 11 rows
2024-06-17 23:57:29,352 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 125 rows
2024-06-17 23:57:35,577 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 75 rows
2024-06-17 23:57:45,755 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 107 rows
2024-06-17 23:57:49,738 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 56 rows
2024-06-17 23:57:53,762 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 39 rows
2024-06-17 23:57:58,809 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 63 rows
2024-06-17 23:57:59,085 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 2 rows
2024-06-17 23:59:03,117 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 942 rows
2024-06-17 23:59:06,429 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 28 rows
2024-06-17 23:59:08,707 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 38 rows
2024-06-17 23:59:28,438 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 262 rows
2024-06-17 23:59:29,811 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 16 rows
2024-06-17 23:59:37,478 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 59 rows
2024-06-17 23:59:42,244 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 65 rows
2024-06-17 23:59:49,771 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 103 rows
2024-06-17 23:59:55,178 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 75 rows
2024-06-17 23:59:57,726 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 46 rows
2024-06-17 23:59:58,743 ｜ INFO ｜ milvus_helpers.py ｜ insert_doc ｜ 85 ｜ Insert vectors to Milvus in collection: productQA_yxxbk_test with 8 rows
