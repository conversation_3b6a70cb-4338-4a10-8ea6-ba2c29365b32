<component name="libraryTable">
  <library name="lib">
    <CLASSES>
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/simpleclient-0.10.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/javax.inject-1.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/msa-starter-frame-3.4.7.1.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/osgi-resource-locator-1.0.3.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/reactor-core-3.4.17.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/msa-starter-redis-3.4.7.1.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/jakarta.xml.bind-api-2.3.3.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/uac-sdk-spring-1.0.2.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/mybatis-3.5.7.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/ant-launcher-1.10.12.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/springfox-spring-web-3.0.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/jakarta.activation-api-1.2.2.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/classgraph-4.8.83.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/aopalliance-1.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/jackson-core-2.14.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/okhttp-4.8.1.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/simple-xml-safe-2.7.1.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/pdfbox-2.0.26.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/kafka-clients-2.7.2.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/poi-ooxml-schemas-4.1.2-20200903124306_modified_talend.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-context-support-5.3.26.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/commons-pool2-2.9.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/jackson-datatype-guava-2.12.6.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/archaius-core-0.7.6.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/jakarta.ws.rs-api-2.1.6.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-web-5.3.26.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-boot-starter-web-2.5.13.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-boot-starter-actuator-2.5.13.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-context-5.3.26.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/msa-basicframework-3.4.7.1.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/snakeyaml-1.33.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/mybatis-spring-2.0.6.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/commons-io-2.11.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/guice-4.1.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/javax.annotation-api-1.3.2.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/springfox-schema-3.0.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/httpcore-nio-4.4.15.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/byte-buddy-1.10.22.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/apollo-client-1.8.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-boot-starter-jdbc-2.5.15.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/jackson-datatype-jdk8-2.12.6.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/minio-8.3.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-jdbc-5.3.29.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/protostuff-core-1.6.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/android-json-0.0.20131108.vaadin1.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-aop-5.3.26.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/protostuff-collectionschema-1.6.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-boot-autoconfigure-2.5.13.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/jackson-module-jaxb-annotations-2.12.6.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/commons-math3-3.6.1.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/slf4j-api-1.7.36.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/commons-lang3-3.12.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/zte-statistics-client-0.0.16-SNAPSHOT.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/springfox-core-3.0.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/springfox-oas-3.0.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/commons-configuration-1.8.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/jersey-client-2.35.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-boot-starter-json-2.5.13.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/j2objc-annotations-1.3.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-boot-starter-logging-2.5.13.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/netty-buffer-4.1.73.Final.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/feign-core-10.12.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/feign-hystrix-10.12.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/kotlin-stdlib-1.6.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/guava-30.1.1-jre.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/httpasyncclient-4.1.5.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/springfox-spring-webflux-3.0.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/log4j-to-slf4j-2.17.2.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/netflix-commons-util-0.1.1.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/jackson-jaxrs-json-provider-2.12.6.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-data-commons-2.5.11.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/LatencyUtils-2.0.3.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/micrometer-registry-prometheus-1.7.11.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/hystrix-core-1.5.18.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/error_prone_annotations-2.10.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/HdrHistogram-2.1.12.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/logback-core-1.2.11.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/springfox-boot-starter-3.0.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/netty-handler-4.1.73.Final.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/protostuff-api-1.6.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/jasypt-1.9.3.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/gson-2.8.9.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/kotlin-stdlib-jdk7-1.5.32.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-core-5.3.26.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-security-crypto-5.6.9.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/okio-jvm-3.5.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/swagger-models-2.1.2.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/mysql-connector-java-high-5.1.46.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-boot-starter-tomcat-2.5.13.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/SparseBitSet-1.2.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/jakarta.annotation-api-1.3.5.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/msa-servicecenter-api-3.4.7.1.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-boot-actuator-2.5.13.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-expression-5.3.26.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/aspectjweaver-1.9.7.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/annotations-13.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/msb-service-1.20.20.01.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/httpclient-4.5.13.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-boot-starter-data-redis-2.5.13.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-data-keyvalue-2.5.11.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/simpleclient_common-0.10.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/mybatis-spring-boot-starter-2.2.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/rxjava-1.3.8.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/logback-classic-1.2.11.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/logback-kafka-appender-0.1.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/ribbon-loadbalancer-2.3.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/fontbox-2.0.26.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/jersey-common-2.35.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-tx-5.3.26.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/netty-transport-4.1.73.Final.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/msa-client-3.4.7.1.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/mybatis-spring-boot-autoconfigure-2.2.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-cloud-starter-3.0.3.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-security-rsa-1.0.10.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/swagger-annotations-1.5.20.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-cloud-commons-3.0.3.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/caffeine-2.9.3.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/curvesapi-1.06.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/classmate-1.5.1.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/jackson-databind-2.14.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/ooxml-schemas-1.4.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/jackson-datatype-jsr310-2.12.6.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/jedis-3.6.3.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/javax.ws.rs-api-2.0.1.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/msa-configcenter-3.4.7.1.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/protostuff-runtime-1.6.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/msa-starter-configcenter-3.4.7.1.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-oxm-5.3.26.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/commons-lang-2.6.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-plugin-metadata-2.0.0.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/poi-4.1.2-20200903124306_modified_talend.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-cloud-config-client-3.0.4.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/kotlin-stdlib-jdk8-1.5.32.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/lettuce-core-6.1.8.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/annotations-2.0.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/commons-compress-1.21.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/netflix-statistics-0.1.1.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-boot-actuator-autoconfigure-2.5.13.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/springfox-spi-3.0.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/msa-metrics-3.4.7.1.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/idatashare-client-java-1.0.17.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/checker-qual-3.7.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/zstd-jni-1.4.5-6.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/kotlin-stdlib-common-1.6.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/netty-common-4.1.73.Final.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/commons-codec-1.15.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/jakarta.inject-2.6.1.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-webmvc-5.3.26.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/springfox-swagger-common-3.0.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/fastjson-1.2.83.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/msa-logback-3.4.7.1.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/okio-3.5.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/failureaccess-1.0.1.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/HikariCP-4.0.3.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/json-sanitizer-1.2.2.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/springfox-swagger-ui-3.0.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/javassist-3.28.0-GA.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-jcl-5.3.26.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/mapstruct-1.3.1.Final.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/jackson-module-parameter-names-2.12.6.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/commons-logging-1.2.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-data-redis-2.5.11.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/swagger-models-1.5.20.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/tomcat-embed-websocket-9.0.71.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/msa-redis-3.4.7.1.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/jackson-jaxrs-base-2.12.6.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/msa-concurrent-3.4.7.1.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/ribbon-core-2.3.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/tomcat-embed-el-9.0.71.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/msa-servicecenter-msb-3.4.7.1.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/servo-core-0.12.21.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/springfox-bean-validators-3.0.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/jackson-annotations-2.14.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/xmlbeans-3.1.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-cloud-context-3.0.3.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/springfox-spring-webmvc-3.0.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/netty-tcnative-classes-2.0.46.Final.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/msa-starter-msb-3.4.7.1.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/reactive-streams-1.0.3.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/commons-collections4-4.4.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/springfox-data-rest-3.0.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/netty-codec-4.1.73.Final.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/uac-sdk-0.1.9.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/snappy-java-1.1.7.7.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/swagger-annotations-2.1.2.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/bcpkix-jdk15on-1.70.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/httpcore-4.4.15.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/jsr305-3.0.1.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/apollo-core-1.8.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/feign-jackson-10.12.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/feign-slf4j-10.12.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/log4j-api-2.17.2.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/lz4-java-1.7.1.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-boot-2.5.13.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/lombok-1.18.24.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/jul-to-slf4j-1.7.36.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/netty-resolver-4.1.73.Final.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/bcutil-jdk15on-1.70.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/bcprov-jdk15on-1.70.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/tomcat-embed-core-9.0.71.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/ant-1.10.12.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/msa-util-3.4.7.1.RELEASE.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/jsoup-1.14.3.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/micrometer-core-1.7.11.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-beans-5.3.26.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/springfox-swagger2-3.0.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-boot-starter-2.5.13.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/poi-ooxml-4.1.2-20200903124306_modified_talend.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-cloud-starter-bootstrap-3.0.3.jar!/" />
      <root url="jar://$PROJECT_DIR$/es/zte-dmt-dmp-doctokg-20248096new/BOOT-INF/lib/spring-plugin-core-2.0.0.RELEASE.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>